# Live Trading Configuration for Smart Trader
# This configuration is optimized for live trading with the 3 working strategies

# Trading Environment
trading:
  mode: "live"  # live, testnet, demo
  account_balance: 100.0  # USD - Your current balance
  max_position_size: 20.0  # Maximum position size in USD (20% of balance)
  min_position_size: 5.0   # Minimum position size in USD
  
  # Risk Management
  risk_management:
    max_daily_loss: 10.0      # Maximum daily loss in USD (10% of balance)
    max_drawdown: 15.0        # Maximum drawdown percentage
    position_sizing: "fixed"   # fixed, percentage, kelly
    leverage: 1               # No leverage for safety
    
  # Take Profit / Stop Loss
  tp_sl:
    default_tp_percent: 2.0   # 2% take profit
    default_sl_percent: 1.5   # 1.5% stop loss
    trailing_stop: true       # Enable trailing stop
    trailing_distance: 0.5    # 0.5% trailing distance
    
  # Order Management
  orders:
    order_type: "market"      # market, limit
    slippage_tolerance: 0.1   # 0.1% slippage tolerance
    timeout_seconds: 30       # Order timeout
    retry_attempts: 3         # Retry failed orders

# Strategy Configuration
strategies:
  # Strategy 1: Smart Model Integrated (Full System)
  smart_model_integrated:
    enabled: true
    priority: 1
    allocation: 40.0          # 40% of available capital
    min_confidence: 0.7       # Minimum signal confidence
    max_positions: 2          # Maximum concurrent positions
    
    # Signal filtering
    filters:
      rsi_range: [25, 75]     # Only trade when RSI in this range
      volume_threshold: 1.5    # Minimum volume multiplier
      market_hours_only: false # Trade 24/7 for crypto
      
    # Model weights
    model_weights:
      technical_analysis: 0.3
      sentiment_analysis: 0.2
      order_flow: 0.2
      llm_analysis: 0.3
      
  # Strategy 2: Smart Strategy Only (Technical Focus)
  smart_strategy_only:
    enabled: true
    priority: 2
    allocation: 35.0          # 35% of available capital
    min_confidence: 0.6       # Lower confidence threshold
    max_positions: 3          # More positions allowed
    
    # Technical indicators
    indicators:
      rsi_period: 14
      macd_fast: 12
      macd_slow: 26
      macd_signal: 9
      bb_period: 20
      bb_std: 2
      
  # Strategy 3: Order Flow Analysis
  order_flow:
    enabled: true
    priority: 3
    allocation: 25.0          # 25% of available capital
    min_confidence: 0.65      # Medium confidence threshold
    max_positions: 2          # Conservative position count
    
    # Order flow parameters
    flow_analysis:
      lookback_periods: 20
      volume_threshold: 2.0
      imbalance_threshold: 0.6

# Market Data Configuration
market_data:
  primary_source: "htx"       # htx, binance
  fallback_source: "binance"  # Fallback if primary fails
  symbols: 
    - "BTC-USDT"
    - "ETH-USDT"
    - "BNB-USDT"
  
  # Data quality
  max_data_age: 10            # Maximum data age in seconds
  min_update_frequency: 1     # Minimum updates per second
  
  # WebSocket settings
  websocket:
    reconnect_attempts: 5
    reconnect_delay: 5
    heartbeat_interval: 30

# Exchange Configuration (HTX)
exchange:
  name: "htx"
  api_key: ""                 # Add your API key
  api_secret: ""              # Add your API secret
  passphrase: ""              # If required
  sandbox: false              # Set to true for testnet
  
  # API settings
  rate_limit: 10              # Requests per second
  timeout: 30                 # Request timeout
  
  # Trading pairs
  default_pair: "BTC-USDT"
  quote_currency: "USDT"

# Monitoring & Alerts
monitoring:
  # Performance tracking
  track_performance: true
  save_trades: true
  log_level: "INFO"
  
  # Alerts (implement webhook/email later)
  alerts:
    large_loss: 5.0           # Alert if single trade loss > $5
    daily_loss_limit: 8.0     # Alert if daily loss > $8
    connection_issues: true   # Alert on connection problems
    
  # Dashboard settings
  dashboard:
    update_interval: 1        # Update every 1 second
    chart_history: 100        # Keep 100 data points
    auto_refresh: true

# Database Configuration
database:
  path: "data/bus.db"
  backup_interval: 3600       # Backup every hour
  retention_days: 30          # Keep data for 30 days
  
  # Performance settings
  wal_mode: true              # Write-Ahead Logging
  cache_size: 10000           # Cache size in pages
  synchronous: "NORMAL"       # FULL, NORMAL, OFF

# Logging Configuration
logging:
  level: "INFO"               # DEBUG, INFO, WARNING, ERROR
  file: "logs/trading.log"
  max_size: "10MB"
  backup_count: 5
  
  # Strategy-specific logging
  strategy_logs:
    smart_model_integrated: "logs/smart_model.log"
    smart_strategy_only: "logs/smart_strategy.log"
    order_flow: "logs/order_flow.log"

# Safety Features
safety:
  # Circuit breakers
  max_consecutive_losses: 5   # Stop after 5 consecutive losses
  cooling_off_period: 3600    # 1 hour cooling off after circuit breaker
  
  # Position limits
  max_total_exposure: 80.0    # Maximum total exposure percentage
  correlation_limit: 0.7      # Don't trade highly correlated pairs
  
  # Emergency stops
  emergency_stop_loss: 20.0   # Emergency stop if total loss > $20
  manual_override: true       # Allow manual intervention

# Development & Testing
development:
  dry_run: false              # Set to true for paper trading
  verbose_logging: true       # Extra logging for debugging
  save_signals: true          # Save all signals for analysis
  backtesting_mode: false     # Disable for live trading
