<!-- Money Circle Unified Header -->
<header class="unified-header">
    <div class="header-content">
        <!-- Branding Section -->
        <div class="header-branding">
            <div class="logo">E</div>
            <div class="brand-text">
                <h1>Money Circle</h1>
                <p>Investment Club</p>
            </div>
        </div>
        
        <!-- Navigation Menu -->
        <nav class="header-nav">
            <a href="/dashboard" class="nav-link {% if request.path == '/dashboard' or request.path == '/personal' %}active{% endif %}">
                📊 Dashboard
            </a>
            <a href="/club" class="nav-link {% if request.path == '/club' %}active{% endif %}">
                🏛️ Club
            </a>
            <a href="/club/strategies" class="nav-link {% if request.path == '/club/strategies' %}active{% endif %}">
                🎯 Strategies
            </a>
            <a href="/club/members" class="nav-link {% if request.path == '/club/members' %}active{% endif %}">
                👥 Members
            </a>
            <a href="/club/analytics" class="nav-link {% if request.path == '/club/analytics' %}active{% endif %}">
                📈 Analytics
            </a>
        </nav>
        
        <!-- User Info Section -->
        <div class="header-user-info">
            <!-- Status Indicators -->
            <div class="status-indicators">
                <div class="status-indicator online">
                    <div class="status-dot"></div>
                    <span>Online</span>
                </div>
                {% if user and user.role == 'admin' %}
                <div class="status-indicator trading">
                    <div class="status-dot"></div>
                    <span>Admin</span>
                </div>
                {% endif %}
            </div>
            
            <!-- User Avatar and Details -->
            {% if user %}
            <div class="user-avatar">
                {{ user.username[0].upper() }}
            </div>
            <div class="user-details">
                <p class="user-welcome">Welcome, {{ user.username }}</p>
                <p class="user-role">{{ user.role.title() }} Member</p>
            </div>
            {% endif %}
            
            <!-- Logout Button -->
            <a href="/logout" class="logout-btn">Logout</a>
        </div>
    </div>
</header>

<!-- Breadcrumb Navigation (optional, shown on specific pages) -->
{% if breadcrumbs %}
<div class="header-breadcrumb">
    <div class="breadcrumb-content">
        {% for breadcrumb in breadcrumbs %}
            {% if loop.last %}
                <span class="breadcrumb-item active">{{ breadcrumb.title }}</span>
            {% else %}
                <a href="{{ breadcrumb.url }}" class="breadcrumb-item">{{ breadcrumb.title }}</a>
                <span class="breadcrumb-separator">›</span>
            {% endif %}
        {% endfor %}
    </div>
</div>
{% endif %}
