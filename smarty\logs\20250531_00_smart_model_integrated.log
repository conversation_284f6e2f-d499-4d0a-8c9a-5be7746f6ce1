2025-05-31 00:11:28,522 - strategy.smart_model_integrated - INFO - [info:89] - 🚀 Strategy logger initialized for smart_model_integrated
2025-05-31 00:11:28,522 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Log file: logs\20250531_00_smart_model_integrated.log
2025-05-31 00:11:28,523 - strategy.smart_model_integrated - INFO - [info:89] - 📊 JSON events: logs\20250531_00_smart_model_integrated_events.json
2025-05-31 00:11:28,523 - strategy.smart_model_integrated - INFO - [info:89] - 🎯 Strategy: smart_model_integrated
2025-05-31 00:11:28,523 - strategy.smart_model_integrated - INFO - [info:89] - 📅 Session: 2025-05-31 00:11:28
2025-05-31 00:11:28,523 - strategy.smart_model_integrated - INFO - [info:89] - 🔧 Log level: INFO
2025-05-31 00:11:28,523 - strategy.smart_model_integrated - INFO - [info:89] - 💻 Python: 3.13.2 | packaged by <PERSON><PERSON>da, Inc. | (main, Feb  6 2025, 18:49:14) [MSC v.1929 64 bit (AMD64)]
2025-05-31 00:11:28,523 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Working directory: C:\Users\<USER>\Documents\dev\smarty
2025-05-31 00:11:28,525 - strategy.smart_model_integrated - INFO - [info:89] - 🐛 Debug mode enabled
2025-05-31 00:11:28,525 - strategy.smart_model_integrated - INFO - [info:89] - 📊 Strategy: smart_integrated
2025-05-31 00:11:28,525 - strategy.smart_model_integrated - INFO - [info:89] - 💰 Symbol: BTC-USDT
2025-05-31 00:11:28,525 - strategy.smart_model_integrated - INFO - [info:89] - 🧪 Testnet: False
2025-05-31 00:11:28,525 - strategy.smart_model_integrated - INFO - [info:89] - 💸 Trading: False
2025-05-31 00:11:28,541 - strategy.smart_model_integrated - INFO - [info:89] - Initialized message bus: SQLiteBus
2025-05-31 00:11:28,541 - strategy.smart_model_integrated - INFO - [info:89] - Set HTX client simulation mode: True
2025-05-31 00:11:28,541 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for HTX client
2025-05-31 00:11:28,541 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Multi-Exchange client
2025-05-31 00:11:28,542 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Binance fallback client
2025-05-31 00:11:28,555 - strategy.smart_model_integrated - WARNING - [warning:93] - SignalStar client not initialized, social sentiment model disabled
2025-05-31 00:11:28,557 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Enhanced LLM Consumer initialized successfully
2025-05-31 00:11:28,569 - strategy.smart_model_integrated - INFO - [info:89] - Starting orchestrator...
2025-05-31 00:11:28,569 - strategy.smart_model_integrated - INFO - [info:89] - 🔄 Attempting Multi-Exchange connection...
2025-05-31 00:11:30,269 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Connected to Multi-Exchange client
2025-05-31 00:11:35,419 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Using Multi-Exchange as primary data source
2025-05-31 00:11:35,421 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Set up message bus subscriptions for Binance fallback data
2025-05-31 00:11:39,645 - strategy.smart_model_integrated - INFO - [info:89] - Loaded 60 historical funding rates for BTC-USDT
2025-05-31 00:11:39,645 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Enhanced LLM Consumer started successfully
2025-05-31 00:11:39,646 - strategy.smart_model_integrated - INFO - [info:89] - 🧠 LLM Model: Unknown
2025-05-31 00:11:39,646 - strategy.smart_model_integrated - INFO - [info:89] - Position manager started
2025-05-31 00:11:39,646 - strategy.smart_model_integrated - INFO - [info:89] - Starting event loop
2025-05-31 00:11:39,646 - strategy.smart_model_integrated - INFO - [info:89] - Started bus maintenance task
2025-05-31 00:11:40,247 - strategy.smart_model_integrated - INFO - [info:89] - Starting account information update task
2025-05-31 00:11:40,247 - strategy.smart_model_integrated - ERROR - [error:97] - Error updating account information: REST client not initialized
2025-05-31 00:11:40,247 - strategy.smart_model_integrated - INFO - [info:89] - Starting health check task
2025-05-31 00:11:40,247 - strategy.smart_model_integrated - INFO - [info:89] - Starting position monitoring task
2025-05-31 00:11:40,247 - strategy.smart_model_integrated - INFO - [info:89] - Starting funding rate fetching task
2025-05-31 00:11:40,251 - strategy.smart_model_integrated - INFO - [info:89] - Starting open interest fetching task
2025-05-31 00:11:40,255 - strategy.smart_model_integrated - INFO - [info:89] - Bus maintenance scheduled every 24 hours, keeping messages for 7 days
2025-05-31 00:18:12,611 - strategy.smart_model_integrated - INFO - [info:89] - 🚀 Strategy logger initialized for smart_model_integrated
2025-05-31 00:18:12,611 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Log file: logs\20250531_00_smart_model_integrated.log
2025-05-31 00:18:12,611 - strategy.smart_model_integrated - INFO - [info:89] - 📊 JSON events: logs\20250531_00_smart_model_integrated_events.json
2025-05-31 00:18:12,611 - strategy.smart_model_integrated - INFO - [info:89] - 🎯 Strategy: smart_model_integrated
2025-05-31 00:18:12,611 - strategy.smart_model_integrated - INFO - [info:89] - 📅 Session: 2025-05-31 00:18:12
2025-05-31 00:18:12,611 - strategy.smart_model_integrated - INFO - [info:89] - 🔧 Log level: INFO
2025-05-31 00:18:12,611 - strategy.smart_model_integrated - INFO - [info:89] - 💻 Python: 3.13.2 | packaged by Anaconda, Inc. | (main, Feb  6 2025, 18:49:14) [MSC v.1929 64 bit (AMD64)]
2025-05-31 00:18:12,611 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Working directory: C:\Users\<USER>\Documents\dev\smarty
2025-05-31 00:18:12,612 - strategy.smart_model_integrated - INFO - [info:89] - 🐛 Debug mode enabled
2025-05-31 00:18:12,612 - strategy.smart_model_integrated - INFO - [info:89] - 📊 Strategy: smart_integrated
2025-05-31 00:18:12,612 - strategy.smart_model_integrated - INFO - [info:89] - 💰 Symbol: BTC-USDT
2025-05-31 00:18:12,612 - strategy.smart_model_integrated - INFO - [info:89] - 🧪 Testnet: False
2025-05-31 00:18:12,612 - strategy.smart_model_integrated - INFO - [info:89] - 💸 Trading: False
2025-05-31 00:18:12,629 - strategy.smart_model_integrated - INFO - [info:89] - Initialized message bus: SQLiteBus
2025-05-31 00:18:12,629 - strategy.smart_model_integrated - INFO - [info:89] - Set HTX client simulation mode: True
2025-05-31 00:18:12,629 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for HTX client
2025-05-31 00:18:12,629 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Multi-Exchange client
2025-05-31 00:18:12,629 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Binance fallback client
2025-05-31 00:18:12,636 - strategy.smart_model_integrated - WARNING - [warning:93] - SignalStar client not initialized, social sentiment model disabled
2025-05-31 00:18:12,638 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Enhanced LLM Consumer initialized successfully
2025-05-31 00:18:12,643 - strategy.smart_model_integrated - INFO - [info:89] - Starting orchestrator...
2025-05-31 00:18:12,643 - strategy.smart_model_integrated - INFO - [info:89] - 🔄 Attempting Multi-Exchange connection...
2025-05-31 00:18:14,336 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Connected to Multi-Exchange client
2025-05-31 00:18:14,338 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Using Multi-Exchange as primary data source
2025-05-31 00:18:14,338 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Set up message bus subscriptions for Binance fallback data
2025-05-31 00:18:15,532 - strategy.smart_model_integrated - INFO - [info:89] - Loaded 60 historical funding rates for BTC-USDT
2025-05-31 00:18:15,532 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Enhanced LLM Consumer started successfully
2025-05-31 00:18:15,534 - strategy.smart_model_integrated - INFO - [info:89] - 🧠 LLM Model: Unknown
2025-05-31 00:18:15,534 - strategy.smart_model_integrated - INFO - [info:89] - Position manager started
2025-05-31 00:18:15,534 - strategy.smart_model_integrated - INFO - [info:89] - Starting event loop
2025-05-31 00:18:15,534 - strategy.smart_model_integrated - INFO - [info:89] - Started bus maintenance task
2025-05-31 00:18:15,534 - strategy.smart_model_integrated - INFO - [info:89] - Starting account information update task
2025-05-31 00:18:15,534 - strategy.smart_model_integrated - ERROR - [error:97] - Error updating account information: REST client not initialized
2025-05-31 00:18:15,534 - strategy.smart_model_integrated - INFO - [info:89] - Starting health check task
2025-05-31 00:18:15,534 - strategy.smart_model_integrated - INFO - [info:89] - Starting position monitoring task
2025-05-31 00:18:15,534 - strategy.smart_model_integrated - INFO - [info:89] - Starting funding rate fetching task
2025-05-31 00:18:15,539 - strategy.smart_model_integrated - INFO - [info:89] - Starting open interest fetching task
2025-05-31 00:18:15,545 - strategy.smart_model_integrated - INFO - [info:89] - Bus maintenance scheduled every 24 hours, keeping messages for 7 days
