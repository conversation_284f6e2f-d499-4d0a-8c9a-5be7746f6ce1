#!/usr/bin/env python3
"""
Money Circle Live Trading Interface
Real-time trading dashboard with advanced order management, risk controls, and strategy automation.
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class OrderStatus(Enum):
    PENDING = "pending"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    PARTIAL = "partial"

class RiskLevel(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class LiveOrder:
    """Live order with real-time tracking."""
    id: str
    user_id: int
    exchange: str
    symbol: str
    side: str  # buy/sell
    order_type: str  # market/limit/stop
    amount: float
    price: Optional[float]
    filled_amount: float
    status: OrderStatus
    strategy_name: str
    risk_level: RiskLevel
    timestamp: datetime
    last_update: datetime
    metadata: Dict[str, Any]

@dataclass
class RiskMetrics:
    """Real-time risk assessment metrics."""
    portfolio_value: float
    daily_pnl: float
    daily_pnl_percent: float
    max_drawdown: float
    position_concentration: float
    leverage_ratio: float
    var_95: float  # Value at Risk 95%
    risk_score: float  # 0-100
    risk_level: RiskLevel
    warnings: List[str]

class LiveTradingInterface:
    """Advanced real-time trading interface with risk management."""

    def __init__(self, db_manager, exchange_manager, user_manager):
        self.db = db_manager
        self.exchange_manager = exchange_manager
        self.user_manager = user_manager

        # Real-time tracking
        self.active_orders: Dict[str, LiveOrder] = {}
        self.user_positions: Dict[int, Dict[str, Any]] = {}
        self.risk_metrics: Dict[int, RiskMetrics] = {}

        # Risk management settings
        self.max_daily_loss_percent = 5.0  # 5% max daily loss
        self.max_position_size_percent = 20.0  # 20% max single position
        self.max_leverage = 3.0  # 3x max leverage

        # Real-time monitoring
        self.monitoring_active = False
        self.update_interval = 1.0  # 1 second updates

        logger.info("[TRADING] Live Trading Interface initialized")

    async def start_real_time_monitoring(self):
        """Start real-time position and risk monitoring."""
        if self.monitoring_active:
            return

        self.monitoring_active = True
        logger.info("📊 Starting real-time trading monitoring...")

        # Start monitoring tasks
        tasks = [
            self._monitor_positions(),
            self._monitor_orders(),
            self._monitor_risk_metrics(),
            self._monitor_market_conditions()
        ]

        await asyncio.gather(*tasks, return_exceptions=True)

    async def place_advanced_order(self, user_id: int, order_params: Dict[str, Any]) -> Optional[LiveOrder]:
        """Place order with advanced risk checks and real-time tracking."""
        try:
            # Pre-trade risk assessment
            risk_check = await self._pre_trade_risk_check(user_id, order_params)
            if not risk_check['approved']:
                logger.warning(f"🚫 Order rejected for user {user_id}: {risk_check['reason']}")
                return None

            # Validate order parameters
            if not self._validate_order_params(order_params):
                logger.error("❌ Invalid order parameters")
                return None

            # Calculate optimal order size based on risk
            optimal_size = await self._calculate_optimal_position_size(user_id, order_params)
            order_params['amount'] = min(order_params['amount'], optimal_size)

            # Place order through exchange
            exchange_order = await self.exchange_manager.place_order_async(
                user_id=user_id,
                **order_params
            )

            if not exchange_order:
                logger.error("❌ Failed to place order on exchange")
                return None

            # Create live order tracking
            live_order = LiveOrder(
                id=exchange_order['id'],
                user_id=user_id,
                exchange=order_params['exchange'],
                symbol=order_params['symbol'],
                side=order_params['side'],
                order_type=order_params.get('type', 'market'),
                amount=order_params['amount'],
                price=order_params.get('price'),
                filled_amount=0.0,
                status=OrderStatus.PENDING,
                strategy_name=order_params.get('strategy', 'Manual'),
                risk_level=RiskLevel(risk_check['risk_level']),
                timestamp=datetime.now(),
                last_update=datetime.now(),
                metadata=order_params.get('metadata', {})
            )

            # Add to active tracking
            self.active_orders[live_order.id] = live_order

            # Log trade activity
            await self._log_trade_activity(user_id, live_order, 'ORDER_PLACED')

            logger.info(f"✅ Advanced order placed: {live_order.id} for user {user_id}")
            return live_order

        except Exception as e:
            logger.error(f"❌ Error placing advanced order: {e}")
            return None

    async def _pre_trade_risk_check(self, user_id: int, order_params: Dict[str, Any]) -> Dict[str, Any]:
        """Comprehensive pre-trade risk assessment."""
        try:
            # Get current risk metrics
            risk_metrics = await self._calculate_risk_metrics(user_id)

            # Check daily loss limits
            if risk_metrics.daily_pnl_percent < -self.max_daily_loss_percent:
                return {
                    'approved': False,
                    'reason': f'Daily loss limit exceeded ({risk_metrics.daily_pnl_percent:.2f}%)',
                    'risk_level': 'critical'
                }

            # Check position concentration
            position_value = order_params['amount'] * order_params.get('price', 1.0)
            concentration = position_value / risk_metrics.portfolio_value * 100

            if concentration > self.max_position_size_percent:
                return {
                    'approved': False,
                    'reason': f'Position size too large ({concentration:.2f}% of portfolio)',
                    'risk_level': 'high'
                }

            # Check leverage limits
            if order_params.get('leverage', 1.0) > self.max_leverage:
                return {
                    'approved': False,
                    'reason': f'Leverage too high ({order_params["leverage"]}x)',
                    'risk_level': 'high'
                }

            # Determine risk level
            risk_level = 'low'
            if concentration > 10.0:
                risk_level = 'medium'
            if concentration > 15.0 or risk_metrics.risk_score > 70:
                risk_level = 'high'

            return {
                'approved': True,
                'reason': 'Risk checks passed',
                'risk_level': risk_level,
                'concentration': concentration,
                'risk_score': risk_metrics.risk_score
            }

        except Exception as e:
            logger.error(f"❌ Error in pre-trade risk check: {e}")
            return {
                'approved': False,
                'reason': f'Risk check error: {e}',
                'risk_level': 'critical'
            }

    async def _calculate_risk_metrics(self, user_id: int) -> RiskMetrics:
        """Calculate comprehensive risk metrics for user."""
        try:
            # Get user positions and portfolio value
            positions = await self.exchange_manager.get_user_positions(user_id)
            portfolio_value = sum(pos.get('value', 0) for pos in positions)

            # Calculate daily P&L
            daily_pnl = sum(pos.get('unrealized_pnl', 0) for pos in positions)
            daily_pnl_percent = (daily_pnl / portfolio_value * 100) if portfolio_value > 0 else 0

            # Calculate max drawdown (simplified)
            max_drawdown = abs(min(0, daily_pnl_percent))

            # Calculate position concentration
            max_position_value = max((pos.get('value', 0) for pos in positions), default=0)
            position_concentration = (max_position_value / portfolio_value * 100) if portfolio_value > 0 else 0

            # Calculate leverage ratio
            total_notional = sum(pos.get('notional', 0) for pos in positions)
            leverage_ratio = (total_notional / portfolio_value) if portfolio_value > 0 else 0

            # Simple VaR calculation (95% confidence)
            var_95 = portfolio_value * 0.02  # 2% of portfolio value

            # Calculate risk score (0-100)
            risk_score = min(100, (
                abs(daily_pnl_percent) * 10 +
                position_concentration * 2 +
                max(0, leverage_ratio - 1) * 20 +
                max_drawdown * 5
            ))

            # Determine risk level
            if risk_score < 30:
                risk_level = RiskLevel.LOW
            elif risk_score < 60:
                risk_level = RiskLevel.MEDIUM
            elif risk_score < 80:
                risk_level = RiskLevel.HIGH
            else:
                risk_level = RiskLevel.CRITICAL

            # Generate warnings
            warnings = []
            if daily_pnl_percent < -3.0:
                warnings.append(f"High daily loss: {daily_pnl_percent:.2f}%")
            if position_concentration > 15.0:
                warnings.append(f"High position concentration: {position_concentration:.1f}%")
            if leverage_ratio > 2.0:
                warnings.append(f"High leverage: {leverage_ratio:.1f}x")

            risk_metrics = RiskMetrics(
                portfolio_value=portfolio_value,
                daily_pnl=daily_pnl,
                daily_pnl_percent=daily_pnl_percent,
                max_drawdown=max_drawdown,
                position_concentration=position_concentration,
                leverage_ratio=leverage_ratio,
                var_95=var_95,
                risk_score=risk_score,
                risk_level=risk_level,
                warnings=warnings
            )

            # Cache for real-time access
            self.risk_metrics[user_id] = risk_metrics

            return risk_metrics

        except Exception as e:
            logger.error(f"❌ Error calculating risk metrics: {e}")
            return RiskMetrics(
                portfolio_value=0, daily_pnl=0, daily_pnl_percent=0,
                max_drawdown=0, position_concentration=0, leverage_ratio=0,
                var_95=0, risk_score=100, risk_level=RiskLevel.CRITICAL,
                warnings=[f"Risk calculation error: {e}"]
            )

    async def _monitor_positions(self):
        """Monitor user positions in real-time."""
        while self.monitoring_active:
            try:
                # Get all active users
                active_users = await self._get_active_trading_users()

                for user_id in active_users:
                    # Update positions
                    positions = await self.exchange_manager.get_user_positions(user_id)
                    self.user_positions[user_id] = positions

                    # Update risk metrics
                    await self._calculate_risk_metrics(user_id)

                await asyncio.sleep(self.update_interval)

            except Exception as e:
                logger.error(f"❌ Error monitoring positions: {e}")
                await asyncio.sleep(5)

    async def _monitor_orders(self):
        """Monitor active orders for status updates."""
        while self.monitoring_active:
            try:
                for order_id, live_order in list(self.active_orders.items()):
                    # Check order status on exchange
                    exchange_order = await self.exchange_manager.get_order_status(
                        live_order.user_id, live_order.exchange, order_id
                    )

                    if exchange_order:
                        # Update order status
                        old_status = live_order.status
                        live_order.status = OrderStatus(exchange_order.get('status', 'pending'))
                        live_order.filled_amount = exchange_order.get('filled', 0)
                        live_order.last_update = datetime.now()

                        # Log status changes
                        if old_status != live_order.status:
                            await self._log_trade_activity(
                                live_order.user_id, live_order, 'STATUS_CHANGE'
                            )

                        # Remove completed orders
                        if live_order.status in [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.REJECTED]:
                            del self.active_orders[order_id]

                await asyncio.sleep(self.update_interval)

            except Exception as e:
                logger.error(f"❌ Error monitoring orders: {e}")
                await asyncio.sleep(5)

    async def _monitor_risk_metrics(self):
        """Monitor risk metrics and trigger alerts."""
        while self.monitoring_active:
            try:
                for user_id, risk_metrics in self.risk_metrics.items():
                    # Check for critical risk levels
                    if risk_metrics.risk_level == RiskLevel.CRITICAL:
                        await self._trigger_risk_alert(user_id, risk_metrics)

                    # Check for stop-loss triggers
                    if risk_metrics.daily_pnl_percent < -self.max_daily_loss_percent:
                        await self._trigger_emergency_stop(user_id, risk_metrics)

                await asyncio.sleep(5)  # Check every 5 seconds

            except Exception as e:
                logger.error(f"❌ Error monitoring risk metrics: {e}")
                await asyncio.sleep(10)

    async def _monitor_market_conditions(self):
        """Monitor market conditions for volatility alerts."""
        while self.monitoring_active:
            try:
                # Monitor market volatility and major price movements
                # This would integrate with market data feeds
                await asyncio.sleep(10)  # Check every 10 seconds

            except Exception as e:
                logger.error(f"❌ Error monitoring market conditions: {e}")
                await asyncio.sleep(30)

    def _validate_order_params(self, params: Dict[str, Any]) -> bool:
        """Validate order parameters."""
        required_fields = ['exchange', 'symbol', 'side', 'amount']
        return all(field in params for field in required_fields)

    async def _calculate_optimal_position_size(self, user_id: int, order_params: Dict[str, Any]) -> float:
        """Calculate optimal position size based on risk management."""
        try:
            risk_metrics = await self._calculate_risk_metrics(user_id)

            # Kelly criterion-based sizing (simplified)
            max_risk_per_trade = 0.02  # 2% max risk per trade
            max_position_value = risk_metrics.portfolio_value * max_risk_per_trade

            price = order_params.get('price', 1.0)
            max_amount = max_position_value / price

            return min(order_params['amount'], max_amount)

        except Exception as e:
            logger.error(f"❌ Error calculating optimal position size: {e}")
            return order_params['amount'] * 0.5  # Conservative fallback

    async def _get_active_trading_users(self) -> List[int]:
        """Get list of users with active trading sessions."""
        try:
            cursor = self.db.conn.execute("""
                SELECT DISTINCT user_id FROM user_sessions
                WHERE expires_at > ? AND is_active = TRUE
            """, (datetime.now(),))

            return [row[0] for row in cursor.fetchall()]

        except Exception as e:
            logger.error(f"❌ Error getting active users: {e}")
            return []

    async def _log_trade_activity(self, user_id: int, order: LiveOrder, activity_type: str):
        """Log trading activity for audit and analysis."""
        try:
            self.db.conn.execute("""
                INSERT INTO trade_activity_log
                (user_id, order_id, activity_type, symbol, amount, price,
                 strategy_name, risk_level, timestamp, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                user_id, order.id, activity_type, order.symbol, order.amount,
                order.price, order.strategy_name, order.risk_level.value,
                datetime.now(), json.dumps(order.metadata)
            ))
            self.db.conn.commit()

        except Exception as e:
            logger.error(f"❌ Error logging trade activity: {e}")

    async def _trigger_risk_alert(self, user_id: int, risk_metrics: RiskMetrics):
        """Trigger risk alert for user."""
        logger.warning(f"🚨 RISK ALERT for user {user_id}: {risk_metrics.warnings}")

        # This would integrate with notification system
        # await self.notification_manager.send_risk_alert(user_id, risk_metrics)

    async def _trigger_emergency_stop(self, user_id: int, risk_metrics: RiskMetrics):
        """Trigger emergency stop for user."""
        logger.critical(f"[EMERGENCY] STOP for user {user_id}: Daily loss limit exceeded")

        # Cancel all pending orders
        user_orders = [order for order in self.active_orders.values() if order.user_id == user_id]
        for order in user_orders:
            await self.exchange_manager.cancel_order(user_id, order.exchange, order.id)

        # This would also close positions if configured
        # await self.exchange_manager.close_all_positions(user_id)

    def get_user_risk_metrics(self, user_id: int) -> Optional[RiskMetrics]:
        """Get current risk metrics for user."""
        return self.risk_metrics.get(user_id)

    def get_active_orders(self, user_id: int) -> List[LiveOrder]:
        """Get active orders for user."""
        return [order for order in self.active_orders.values() if order.user_id == user_id]

    async def stop_monitoring(self):
        """Stop real-time monitoring."""
        self.monitoring_active = False
        logger.info("🛑 Live trading monitoring stopped")
