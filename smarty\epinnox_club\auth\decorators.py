#!/usr/bin/env python3
"""
Money Circle Authentication Decorators
Decorators for protecting routes and checking permissions.
"""

import logging
from functools import wraps
from typing import List, Optional, Callable, Any
from aiohttp import web
from auth.user_manager import UserManager

logger = logging.getLogger(__name__)

def require_auth(user_manager: UserManager):
    """Decorator to require authentication for routes."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(self, request: web.Request) -> web.Response:
            # Get session from cookie
            session_id = request.cookies.get('session_id')
            if not session_id:
                logger.warning("🚫 No session cookie found")
                return web.Response(
                    status=302,
                    headers={'Location': '/login'}
                )
            
            # Validate session
            session = user_manager.validate_session(session_id)
            if not session:
                logger.warning("🚫 Invalid or expired session")
                response = web.Response(
                    status=302,
                    headers={'Location': '/login'}
                )
                response.del_cookie('session_id')
                return response
            
            # Add user info to request
            request['user'] = session
            return await func(self, request)
        
        return wrapper
    return decorator

def require_role(user_manager: UserManager, required_roles: List[str]):
    """Decorator to require specific roles for routes."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(self, request: web.Request) -> web.Response:
            # First check authentication
            session_id = request.cookies.get('session_id')
            if not session_id:
                return web.Response(
                    status=302,
                    headers={'Location': '/login'}
                )
            
            session = user_manager.validate_session(session_id)
            if not session:
                response = web.Response(
                    status=302,
                    headers={'Location': '/login'}
                )
                response.del_cookie('session_id')
                return response
            
            # Check role
            user_role = session.get('role', 'viewer')
            if user_role not in required_roles:
                logger.warning(f"🚫 Access denied: {session['username']} ({user_role}) tried to access {required_roles} only route")
                return web.json_response(
                    {'error': 'Insufficient permissions'},
                    status=403
                )
            
            # Add user info to request
            request['user'] = session
            return await func(self, request)
        
        return wrapper
    return decorator

def require_admin(user_manager: UserManager):
    """Decorator to require admin role."""
    return require_role(user_manager, ['admin'])

def require_member_or_admin(user_manager: UserManager):
    """Decorator to require member or admin role."""
    return require_role(user_manager, ['member', 'admin'])

class AuthMiddleware:
    """Authentication middleware for aiohttp."""
    
    def __init__(self, user_manager: UserManager):
        self.user_manager = user_manager
        
        # Public routes that don't require authentication
        self.public_routes = {
            '/login',
            '/register',
            '/static',
            '/favicon.ico'
        }
    
    async def __call__(self, request: web.Request, handler: Callable) -> web.Response:
        """Middleware handler."""
        path = request.path
        
        # Skip authentication for public routes
        if any(path.startswith(route) for route in self.public_routes):
            return await handler(request)
        
        # Check for session
        session_id = request.cookies.get('session_id')
        if session_id:
            session = self.user_manager.validate_session(session_id)
            if session:
                request['user'] = session
                return await handler(request)
        
        # Redirect to login for protected routes
        if request.method == 'GET' and not path.startswith('/api/'):
            return web.Response(
                status=302,
                headers={'Location': '/login'}
            )
        else:
            # Return 401 for API routes
            return web.json_response(
                {'error': 'Authentication required'},
                status=401
            )

def get_current_user(request: web.Request) -> Optional[dict]:
    """Get current user from request."""
    return request.get('user')

def is_admin(request: web.Request) -> bool:
    """Check if current user is admin."""
    user = get_current_user(request)
    return user and user.get('role') == 'admin'

def is_member_or_admin(request: web.Request) -> bool:
    """Check if current user is member or admin."""
    user = get_current_user(request)
    return user and user.get('role') in ['member', 'admin']

def check_permission(request: web.Request, required_roles: List[str]) -> bool:
    """Check if user has required permission."""
    user = get_current_user(request)
    if not user:
        return False
    
    user_role = user.get('role', 'viewer')
    return user_role in required_roles

async def handle_login_required(request: web.Request) -> web.Response:
    """Handle login required response."""
    if request.method == 'GET' and not request.path.startswith('/api/'):
        return web.Response(
            status=302,
            headers={'Location': '/login'}
        )
    else:
        return web.json_response(
            {'error': 'Authentication required'},
            status=401
        )

async def handle_permission_denied(request: web.Request) -> web.Response:
    """Handle permission denied response."""
    user = get_current_user(request)
    username = user.get('username', 'unknown') if user else 'anonymous'
    
    logger.warning(f"🚫 Permission denied for user: {username}")
    
    if request.method == 'GET' and not request.path.startswith('/api/'):
        return web.Response(
            status=302,
            headers={'Location': '/dashboard?error=permission_denied'}
        )
    else:
        return web.json_response(
            {'error': 'Insufficient permissions'},
            status=403
        )
