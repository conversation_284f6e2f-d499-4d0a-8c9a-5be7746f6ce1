/* Money Circle Dashboard Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    min-height: 100vh;
    color: #ffffff;
}

.dashboard-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 30px;
}

.header-left h1 {
    color: #FFD700;
    font-size: 2em;
    margin-bottom: 5px;
}

.user-info {
    color: #94a3b8;
    font-size: 0.9em;
}

.header-right {
    display: flex;
    gap: 20px;
}

.nav-link {
    color: #e2e8f0;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #FFD700;
}

/* Portfolio Overview */
.portfolio-overview {
    margin-bottom: 40px;
}

.portfolio-overview h2 {
    color: #FFD700;
    margin-bottom: 20px;
    font-size: 1.5em;
}

.portfolio-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.stat-card {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 12px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.stat-card h3 {
    color: #94a3b8;
    font-size: 0.9em;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    font-size: 2em;
    font-weight: 700;
    color: #ffffff;
}

.stat-value.positive {
    color: #4CAF50;
}

.stat-value.negative {
    color: #f44336;
}

/* Exchange Accounts */
.exchange-accounts {
    margin-bottom: 40px;
}

.exchange-accounts h2 {
    color: #FFD700;
    margin-bottom: 20px;
    font-size: 1.5em;
}

.exchanges-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.exchange-card {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.exchange-card.connected {
    border-color: rgba(76, 175, 80, 0.3);
}

.exchange-card.disconnected {
    border-color: rgba(244, 67, 54, 0.3);
}

.exchange-card h4 {
    color: #FFD700;
    margin-bottom: 10px;
    font-size: 1.2em;
}

.connection-status {
    margin-bottom: 15px;
    font-size: 0.9em;
}

.balance-summary {
    margin-bottom: 15px;
}

.balance-item {
    color: #94a3b8;
    font-size: 0.85em;
    margin-bottom: 5px;
}

.exchange-actions {
    display: flex;
    gap: 10px;
}

.exchange-actions button {
    padding: 6px 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    background: rgba(0, 0, 0, 0.2);
    color: #e2e8f0;
    cursor: pointer;
    font-size: 0.8em;
    transition: all 0.3s ease;
}

.exchange-actions button:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: #FFD700;
}

.add-exchange-btn {
    padding: 12px 24px;
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    color: #000000;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.add-exchange-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

/* Tables */
.positions-table, .trades-table {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

table th {
    color: #FFD700;
    text-align: left;
    padding: 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8em;
    letter-spacing: 0.5px;
}

table td {
    padding: 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    color: #e2e8f0;
}

table tr:hover {
    background: rgba(255, 255, 255, 0.05);
}

.buy {
    color: #4CAF50;
}

.sell {
    color: #f44336;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: rgba(0, 0, 0, 0.9);
    margin: 10% auto;
    padding: 30px;
    border-radius: 16px;
    width: 90%;
    max-width: 500px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-content h3 {
    color: #FFD700;
    margin-bottom: 20px;
    text-align: center;
}

.modal-content select,
.modal-content input {
    width: 100%;
    padding: 12px;
    margin-bottom: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.3);
    color: #ffffff;
    font-size: 14px;
}

.modal-content select:focus,
.modal-content input:focus {
    outline: none;
    border-color: #FFD700;
    box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
}

.modal-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
}

.modal-buttons button {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.modal-buttons button[type="submit"] {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    color: #000000;
}

.modal-buttons button[type="button"] {
    background: rgba(255, 255, 255, 0.1);
    color: #e2e8f0;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-buttons button:hover {
    transform: translateY(-1px);
}

/* Trading Interface */
.trading-interface {
    margin-bottom: 40px;
}

.trading-interface h2 {
    color: #FFD700;
    margin-bottom: 20px;
    font-size: 1.5em;
}

.trading-controls {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.trading-form {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.trading-form h3 {
    color: #FFD700;
    margin-bottom: 15px;
    font-size: 1.1em;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 15px;
}

.trading-form select,
.trading-form input {
    padding: 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    background: rgba(0, 0, 0, 0.3);
    color: #ffffff;
    font-size: 14px;
}

.trading-form select:focus,
.trading-form input:focus {
    outline: none;
    border-color: #FFD700;
    box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.1);
}

.form-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.trade-btn {
    flex: 1;
    padding: 12px;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.trade-btn.buy {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
}

.trade-btn.sell {
    background: linear-gradient(135deg, #f44336, #da190b);
    color: white;
}

.trade-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.market-info {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.market-info h3 {
    color: #FFD700;
    margin-bottom: 15px;
    font-size: 1.1em;
}

.price-tickers {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.ticker {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    align-items: center;
    padding: 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.ticker .symbol {
    font-weight: 600;
    color: #e2e8f0;
}

.ticker .price {
    font-weight: 700;
    color: #ffffff;
    text-align: center;
}

.ticker .change {
    text-align: right;
    font-weight: 600;
}

.ticker .change.positive {
    color: #4CAF50;
}

.ticker .change.negative {
    color: #f44336;
}

/* Responsive design */
@media (max-width: 768px) {
    .dashboard-container {
        padding: 10px;
    }

    .dashboard-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .header-right {
        justify-content: center;
    }

    .portfolio-stats {
        grid-template-columns: 1fr;
    }

    .exchanges-grid {
        grid-template-columns: 1fr;
    }

    .trading-controls {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }
}
