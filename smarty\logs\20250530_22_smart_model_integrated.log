2025-05-30 22:47:43,050 - strategy.smart_model_integrated - INFO - [info:89] - 🚀 Strategy logger initialized for smart_model_integrated
2025-05-30 22:47:43,050 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Log file: logs\20250530_22_smart_model_integrated.log
2025-05-30 22:47:43,050 - strategy.smart_model_integrated - INFO - [info:89] - 📊 JSON events: logs\20250530_22_smart_model_integrated_events.json
2025-05-30 22:47:43,051 - strategy.smart_model_integrated - INFO - [info:89] - 🎯 Strategy: smart_model_integrated
2025-05-30 22:47:43,051 - strategy.smart_model_integrated - INFO - [info:89] - 📅 Session: 2025-05-30 22:47:43
2025-05-30 22:47:43,052 - strategy.smart_model_integrated - INFO - [info:89] - 🔧 Log level: INFO
2025-05-30 22:47:43,052 - strategy.smart_model_integrated - INFO - [info:89] - 💻 Python: 3.9.13 (tags/v3.9.13:6de2ca5, May 17 2022, 16:36:42) [MSC v.1929 64 bit (AMD64)]
2025-05-30 22:47:43,052 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Working directory: C:\Users\<USER>\Documents\dev\smarty
2025-05-30 22:47:43,053 - strategy.smart_model_integrated - INFO - [info:89] - 🐛 Debug mode enabled
2025-05-30 22:47:43,054 - strategy.smart_model_integrated - INFO - [info:89] - 📊 Strategy: smart_integrated
2025-05-30 22:47:43,054 - strategy.smart_model_integrated - INFO - [info:89] - 💰 Symbol: BTC-USDT
2025-05-30 22:47:43,054 - strategy.smart_model_integrated - INFO - [info:89] - 🧪 Testnet: False
2025-05-30 22:47:43,054 - strategy.smart_model_integrated - INFO - [info:89] - 💸 Trading: False
2025-05-30 22:47:43,069 - strategy.smart_model_integrated - INFO - [info:89] - Initialized message bus: SQLiteBus
2025-05-30 22:47:43,069 - strategy.smart_model_integrated - INFO - [info:89] - Set HTX client simulation mode: True
2025-05-30 22:47:43,069 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for HTX client
2025-05-30 22:47:43,070 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Multi-Exchange client
2025-05-30 22:47:43,070 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Binance fallback client
