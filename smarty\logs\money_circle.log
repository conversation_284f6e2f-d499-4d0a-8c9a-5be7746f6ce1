2025-05-31 13:27:11,506 - database.models - INFO - [OK] Database migrations completed successfully
2025-05-31 13:27:11,506 - database.models - INFO - [OK] Database initialized successfully
2025-05-31 13:27:11,510 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:27:11,516 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:27:11,520 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:27:11,526 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:27:11,530 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:27:11,535 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:27:11,539 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:27:11,544 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:27:11,549 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:27:11,554 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:27:11,558 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:27:11,564 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:27:11,568 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:27:11,573 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:27:11,578 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:27:11,583 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:27:11,587 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:27:11,597 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:27:11,698 - market_data.advanced_market_data_manager - INFO - [DATA] Advanced Market Data Manager initialized
2025-05-31 13:27:11,698 - market_data.market_data_api - INFO - 📡 Market Data API initialized
2025-05-31 13:27:11,698 - market_data.websocket_streamer - INFO - 🌐 WebSocket Streamer initialized
2025-05-31 13:27:11,698 - notifications.notification_manager - INFO - 🔔 Notification Manager initialized
2025-05-31 13:27:11,698 - trading.live_trading_interface - INFO - [TRADING] Live Trading Interface initialized
2025-05-31 13:27:11,698 - strategies.live_strategy_engine - INFO - ⚡ Live Strategy Engine initialized
2025-05-31 13:27:11,698 - __main__ - INFO - [OK] Advanced trading components with real-time market data initialized
2025-05-31 13:27:11,699 - __main__ - INFO - [STARTUP] Money Circle initialized with config: development
2025-05-31 13:27:11,701 - market_data.market_data_api - INFO - ✅ Market Data API routes configured
2025-05-31 13:27:11,701 - __main__ - INFO - [OK] Market Data API routes configured
2025-05-31 13:27:11,701 - __main__ - INFO - [OK] Market Data WebSocket routes configured
2025-05-31 13:27:11,701 - __main__ - INFO - [OK] Routes configured
2025-05-31 13:27:11,702 - __main__ - INFO - [OK] Web application configured
2025-05-31 13:27:11,708 - __main__ - INFO - [SERVER] Money Circle running at http://localhost:8080
2025-05-31 13:27:11,708 - __main__ - INFO - [READY] Platform ready for Money Circle investment club members
2025-05-31 13:27:11,708 - __main__ - INFO - [DATA] Advanced market data manager started
2025-05-31 13:27:11,708 - __main__ - INFO - 🌐 Market data WebSocket streamer subscribed to updates
2025-05-31 13:27:11,708 - __main__ - INFO - 📡 Notification system started
2025-05-31 13:27:11,709 - __main__ - INFO - ⚡ Live trading monitoring started
2025-05-31 13:27:11,709 - __main__ - INFO - 🤖 Strategy automation started
2025-05-31 13:27:11,709 - market_data.advanced_market_data_manager - INFO - 🚀 Starting advanced market data manager...
2025-05-31 13:27:11,709 - notifications.notification_manager - INFO - 📡 Starting notification delivery system...
2025-05-31 13:27:11,709 - trading.live_trading_interface - INFO - 📊 Starting real-time trading monitoring...
2025-05-31 13:27:11,709 - strategies.live_strategy_engine - INFO - 🚀 Starting live strategy automation...
2025-05-31 13:27:11,710 - market_data.advanced_market_data_manager - INFO - 🔄 Connecting to Binance WebSocket...
2025-05-31 13:27:11,727 - market_data.advanced_market_data_manager - INFO - 🔄 Connecting to HTX WebSocket...
2025-05-31 13:27:11,728 - market_data.advanced_market_data_manager - INFO - 🔄 Connecting to Bybit Spot WebSocket...
2025-05-31 13:27:11,736 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:27:11,736 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 0.0% (0 excellent, 0 good)
2025-05-31 13:27:11,736 - notifications.notification_manager - ERROR - ❌ Error cleaning up notifications: no such table: notifications
2025-05-31 13:27:11,737 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:12,760 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:13,201 - market_data.advanced_market_data_manager - INFO - ✅ Connected to Binance WebSocket
2025-05-31 13:27:13,238 - market_data.advanced_market_data_manager - INFO - ✅ Connected to Bybit Spot WebSocket
2025-05-31 13:27:13,238 - market_data.advanced_market_data_manager - INFO - 📡 Subscribed to Bybit tickers: ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
2025-05-31 13:27:13,352 - market_data.advanced_market_data_manager - INFO - ✅ Connected to HTX WebSocket
2025-05-31 13:27:13,526 - market_data.advanced_market_data_manager - INFO - ✅ Bybit subscription confirmed: subscribe
2025-05-31 13:27:13,750 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:14,758 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:15,754 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:16,751 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:17,733 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:18,744 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:19,748 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:20,745 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:21,759 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:22,737 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:23,747 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:24,744 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:25,755 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:26,751 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:27,745 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:28,752 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:29,744 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:30,752 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:31,743 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:32,740 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:33,747 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:34,756 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:35,748 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:36,745 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:37,757 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:38,749 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:39,742 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:40,751 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:41,752 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:42,768 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:43,782 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:44,794 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:45,805 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:46,795 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:47,818 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:48,827 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:49,826 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:50,826 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:51,827 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:52,835 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:53,845 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:54,854 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:55,855 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:56,847 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:57,850 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:58,862 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:27:59,871 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:00,887 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:01,915 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:02,920 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:03,928 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:04,925 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:05,932 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:06,926 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:07,933 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:08,934 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:09,945 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:10,942 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:11,751 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:28:11,751 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:28:11,954 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:12,954 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:13,953 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:14,963 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:15,974 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:16,970 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:17,982 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:19,003 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:20,015 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:21,034 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:22,030 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:23,042 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:24,037 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:25,033 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:26,045 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:27,050 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:28,048 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:29,058 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:30,055 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:31,062 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:32,087 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:33,097 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:34,107 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:35,134 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:35,730 - aiohttp.access - INFO - ::1 [31/May/2025:12:28:35 -0600] "GET / HTTP/1.1" 302 132 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:28:35,751 - aiohttp.access - INFO - ::1 [31/May/2025:12:28:35 -0600] "GET /login HTTP/1.1" 200 17109 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:28:35,784 - aiohttp.access - INFO - ::1 [31/May/2025:12:28:35 -0600] "GET /static/css/unified_header.css HTTP/1.1" 200 237 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:28:35,785 - aiohttp.access - INFO - ::1 [31/May/2025:12:28:35 -0600] "GET /static/css/design_system.css HTTP/1.1" 200 237 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:28:35,785 - aiohttp.access - INFO - ::1 [31/May/2025:12:28:35 -0600] "GET /static/js/common.js HTTP/1.1" 200 252 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:28:35,785 - aiohttp.access - INFO - ::1 [31/May/2025:12:28:35 -0600] "GET /static/css/unified_footer.css HTTP/1.1" 200 237 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:28:35,794 - aiohttp.access - INFO - ::1 [31/May/2025:12:28:35 -0600] "GET /static/css/auth.css HTTP/1.1" 200 236 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:28:36,050 - aiohttp.access - INFO - ::1 [31/May/2025:12:28:36 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 172 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:28:36,080 - aiohttp.access - INFO - ::1 [31/May/2025:12:28:36 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 172 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:28:36,109 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:37,113 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:38,111 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:39,129 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:40,139 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:41,152 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:42,165 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:43,156 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:44,182 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:45,192 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:46,188 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:47,200 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:48,220 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:49,232 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:50,242 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:51,245 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:52,245 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:53,256 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:54,247 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:55,246 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:56,242 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:57,247 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:58,246 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:28:59,246 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:00,248 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:01,250 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:02,247 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:03,249 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:04,247 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:05,246 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:06,235 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:07,247 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:08,246 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:09,246 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:10,247 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:11,246 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:11,775 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:29:11,775 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:29:12,243 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:13,247 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:14,248 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:15,247 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:16,246 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:17,247 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:18,247 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:19,235 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:20,247 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:21,254 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:22,246 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:23,247 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:24,238 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:25,245 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:26,245 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:27,247 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:28,247 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:29,246 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:30,246 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:31,246 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:32,246 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:33,247 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:34,246 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:35,246 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:36,239 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:37,247 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:38,254 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:39,246 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:40,246 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:41,258 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:42,246 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:43,243 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:44,247 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:45,246 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:46,247 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:47,267 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:48,273 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:49,269 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:50,266 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:51,271 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:52,287 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:53,300 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:54,310 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:55,338 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:56,314 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:57,330 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:58,339 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:29:59,343 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:00,356 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:01,365 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:02,371 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:03,378 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:04,373 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:05,380 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:06,379 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:07,387 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:08,399 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:09,410 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:10,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:11,390 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:11,788 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:30:11,788 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:30:12,397 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:13,404 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:14,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:15,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:16,387 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:17,397 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:18,394 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:19,405 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:20,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:21,410 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:22,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:23,410 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:24,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:25,412 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:26,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:27,412 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:28,407 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:29,415 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:30,403 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:31,391 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:32,393 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:33,401 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:34,399 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:35,404 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:36,408 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:37,415 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:38,414 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:39,407 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:40,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:41,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:42,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:43,409 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:44,414 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:45,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:46,406 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:47,405 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:48,407 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:49,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:50,407 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:51,393 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:52,412 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:53,414 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:54,410 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:55,412 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:56,412 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:57,414 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:58,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:30:59,406 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:00,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:01,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:02,407 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:03,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:04,403 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:05,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:06,393 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:07,400 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:08,393 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:09,404 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:10,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:11,391 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:11,798 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:31:11,798 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:31:12,390 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:13,398 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:14,394 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:15,406 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:16,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:17,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:18,414 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:19,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:20,408 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:21,414 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:22,405 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:23,406 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:24,405 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:25,415 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:26,411 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:27,415 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:28,396 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:29,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:30,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:31,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:32,392 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:33,391 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:34,398 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:35,389 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:36,401 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:37,395 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:38,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:39,414 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:40,410 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:41,406 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:42,398 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:43,391 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:44,398 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:45,396 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:46,390 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:47,400 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:48,406 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:49,400 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:50,403 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:51,414 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:52,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:53,405 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:54,383 - auth.user_manager - WARNING - 🚫 Failed login attempt for: trader_alex
2025-05-31 13:31:54,383 - auth.user_manager - WARNING - 🚫 Failed login attempt for: ::1
2025-05-31 13:31:54,383 - aiohttp.access - INFO - ::1 [31/May/2025:12:31:54 -0600] "POST /login HTTP/1.1" 302 318 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:31:54,388 - aiohttp.access - INFO - ::1 [31/May/2025:12:31:54 -0600] "GET /login?error=invalid_credentials HTTP/1.1" 200 17259 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:31:54,389 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:54,464 - aiohttp.access - INFO - ::1 [31/May/2025:12:31:54 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 172 "http://localhost:8080/login?error=invalid_credentials" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:31:55,395 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:56,402 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:57,208 - auth.user_manager - WARNING - 🚫 Failed login attempt for: crypto_sarah
2025-05-31 13:31:57,208 - auth.user_manager - WARNING - 🚫 Failed login attempt for: ::1
2025-05-31 13:31:57,209 - aiohttp.access - INFO - ::1 [31/May/2025:12:31:57 -0600] "POST /login HTTP/1.1" 302 318 "http://localhost:8080/login?error=invalid_credentials" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:31:57,212 - aiohttp.access - INFO - ::1 [31/May/2025:12:31:57 -0600] "GET /login?error=invalid_credentials HTTP/1.1" 200 17259 "http://localhost:8080/login?error=invalid_credentials" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:31:57,288 - aiohttp.access - INFO - ::1 [31/May/2025:12:31:57 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 172 "http://localhost:8080/login?error=invalid_credentials" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:31:57,411 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:58,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:31:59,407 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:00,406 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:01,396 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:02,410 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:03,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:04,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:05,411 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:06,407 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:07,415 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:08,411 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:09,414 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:10,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:11,415 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:11,807 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:32:11,807 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:32:12,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:13,411 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:14,407 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:15,404 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:16,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:17,407 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:18,414 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:19,409 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:20,414 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:21,414 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:22,406 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:23,414 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:24,408 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:25,414 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:26,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:27,287 - aiohttp.access - INFO - ::1 [31/May/2025:12:32:27 -0600] "GET /dashboard HTTP/1.1" 302 132 "http://localhost:8080/login?error=invalid_credentials" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:32:27,291 - aiohttp.access - INFO - ::1 [31/May/2025:12:32:27 -0600] "GET /login HTTP/1.1" 200 17109 "http://localhost:8080/login?error=invalid_credentials" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:32:27,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:27,441 - aiohttp.access - INFO - ::1 [31/May/2025:12:32:27 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 172 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:32:27,931 - aiohttp.access - INFO - ::1 [31/May/2025:12:32:27 -0600] "GET /club HTTP/1.1" 302 132 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:32:27,936 - aiohttp.access - INFO - ::1 [31/May/2025:12:32:27 -0600] "GET /login HTTP/1.1" 200 17109 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:32:28,052 - aiohttp.access - INFO - ::1 [31/May/2025:12:32:28 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 172 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:32:28,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:28,775 - aiohttp.access - INFO - ::1 [31/May/2025:12:32:28 -0600] "GET /club/strategies HTTP/1.1" 302 132 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:32:28,778 - aiohttp.access - INFO - ::1 [31/May/2025:12:32:28 -0600] "GET /login HTTP/1.1" 200 17109 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:32:28,861 - aiohttp.access - INFO - ::1 [31/May/2025:12:32:28 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 172 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:32:29,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:29,472 - aiohttp.access - INFO - ::1 [31/May/2025:12:32:29 -0600] "GET /club/members HTTP/1.1" 302 132 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:32:29,476 - aiohttp.access - INFO - ::1 [31/May/2025:12:32:29 -0600] "GET /login HTTP/1.1" 200 17109 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:32:29,558 - aiohttp.access - INFO - ::1 [31/May/2025:12:32:29 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 172 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:32:30,155 - aiohttp.access - INFO - ::1 [31/May/2025:12:32:30 -0600] "GET /club/analytics HTTP/1.1" 302 132 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:32:30,158 - aiohttp.access - INFO - ::1 [31/May/2025:12:32:30 -0600] "GET /login HTTP/1.1" 200 17109 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:32:30,266 - aiohttp.access - INFO - ::1 [31/May/2025:12:32:30 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 172 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:32:30,409 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:31,414 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:32,406 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:32,855 - aiohttp.access - INFO - ::1 [31/May/2025:12:32:32 -0600] "GET /logout HTTP/1.1" 302 217 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:32:32,858 - aiohttp.access - INFO - ::1 [31/May/2025:12:32:32 -0600] "GET /login HTTP/1.1" 200 17109 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:32:32,931 - aiohttp.access - INFO - ::1 [31/May/2025:12:32:32 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 172 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:32:33,396 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:34,397 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:35,392 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:36,392 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:37,402 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:38,406 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:39,398 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:40,407 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:41,405 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:42,412 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:43,405 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:44,414 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:45,402 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:46,391 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:47,405 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:48,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:49,414 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:50,415 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:51,392 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:52,391 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:53,403 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:54,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:55,415 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:56,407 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:57,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:58,395 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:32:59,401 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:00,392 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:01,397 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:02,390 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:03,396 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:04,407 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:05,414 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:06,407 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:07,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:08,205 - aiohttp.access - INFO - ::1 [31/May/2025:12:33:08 -0600] "GET /register HTTP/1.1" 200 8666 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:33:08,230 - aiohttp.access - INFO - ::1 [31/May/2025:12:33:08 -0600] "GET /static/css/club.css HTTP/1.1" 200 238 "http://localhost:8080/register" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:33:08,339 - aiohttp.access - INFO - ::1 [31/May/2025:12:33:08 -0600] "GET /favicon.ico HTTP/1.1" 404 174 "http://localhost:8080/register" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:33:08,407 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:09,387 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:10,397 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:11,398 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:11,817 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:33:11,817 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:33:12,393 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:13,391 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:14,403 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:15,414 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:16,411 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:17,408 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:18,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:19,153 - market_data.advanced_market_data_manager - WARNING - ⚠️ HTX WebSocket connection closed: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:33:19,155 - market_data.advanced_market_data_manager - INFO - 🔄 Reconnecting to HTX WebSocket in 5 seconds...
2025-05-31 13:33:19,417 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:20,412 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:21,412 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:22,414 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:23,409 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:24,170 - market_data.advanced_market_data_manager - INFO - 🔄 Connecting to HTX WebSocket...
2025-05-31 13:33:24,406 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:24,930 - aiohttp.access - INFO - ::1 [31/May/2025:12:33:24 -0600] "POST /register HTTP/1.1" 302 359 "http://localhost:8080/register" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:33:24,933 - aiohttp.access - INFO - ::1 [31/May/2025:12:33:24 -0600] "GET /register?error=server_error&username=dem29&email=<EMAIL> HTTP/1.1" 200 8805 "http://localhost:8080/register" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:33:25,415 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:25,664 - market_data.advanced_market_data_manager - INFO - ✅ Connected to HTX WebSocket
2025-05-31 13:33:26,410 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:27,392 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:28,406 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:29,414 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:30,414 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:31,415 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:32,415 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:33,415 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:34,407 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:35,404 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:36,393 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:37,407 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:38,414 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:39,032 - aiohttp.access - INFO - ::1 [31/May/2025:12:33:39 -0600] "POST /register HTTP/1.1" 302 361 "http://localhost:8080/register?error=server_error&username=dem29&email=<EMAIL>" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:33:39,035 - aiohttp.access - INFO - ::1 [31/May/2025:12:33:39 -0600] "GET /register?error=server_error&username=dem292&email=<EMAIL> HTTP/1.1" 200 8807 "http://localhost:8080/register?error=server_error&username=dem29&email=<EMAIL>" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:33:39,415 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:40,405 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:41,416 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:42,409 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:43,404 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:44,412 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:45,412 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:46,408 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:47,405 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:48,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:49,411 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:50,409 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:51,406 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:52,405 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:53,415 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:54,407 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:55,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:56,412 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:57,408 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:58,396 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:33:59,405 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:00,389 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:01,399 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:02,389 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:03,390 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:04,397 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:05,394 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:06,392 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:07,401 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:08,401 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:09,392 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:10,390 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:11,403 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:11,838 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:34:11,838 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:34:12,414 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:13,405 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:14,407 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:15,415 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:16,411 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:17,415 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:18,415 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:19,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:20,408 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:21,418 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:22,414 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:23,412 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:24,408 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:25,414 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:26,415 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:27,415 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:28,392 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:29,398 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:30,396 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:31,393 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:32,406 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:33,416 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:34,413 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:35,408 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:36,415 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:37,405 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:38,410 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:39,417 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:40,416 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:41,416 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:42,415 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:43,411 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:44,416 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:45,415 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:46,415 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:47,406 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:48,416 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:49,417 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:50,407 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:51,412 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:52,419 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:53,435 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:54,458 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:55,464 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:56,472 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:57,487 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:58,500 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:34:59,512 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:00,513 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:01,537 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:02,541 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:03,550 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:04,561 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:05,565 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:06,564 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:07,587 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:08,599 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:09,613 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:10,620 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:11,620 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:11,839 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:35:11,839 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:35:12,620 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:13,616 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:14,620 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:15,620 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:16,620 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:17,622 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:18,619 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:19,621 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:20,619 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:21,620 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:22,626 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:23,608 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:24,616 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:25,620 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:26,619 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:27,621 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:28,618 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:29,619 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:30,618 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:31,618 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:32,619 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:33,613 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:34,620 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:35,617 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:36,617 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:37,619 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:38,620 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:39,617 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:40,635 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:41,620 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:42,617 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:43,618 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:44,617 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:45,620 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:46,617 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:47,625 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:48,620 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:49,620 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:50,636 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:51,608 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:52,619 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:53,617 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:54,620 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:55,620 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:56,614 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:57,620 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:58,636 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:35:59,661 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:00,677 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:01,698 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:02,701 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:03,722 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:04,732 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:05,742 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:06,750 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:07,765 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:08,778 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:09,789 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:10,783 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:11,802 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:11,831 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:36:11,832 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:36:12,815 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:13,828 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:14,831 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:15,832 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:16,832 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:17,830 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:18,832 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:19,842 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:20,850 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:21,863 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:22,873 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:23,898 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:24,894 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:25,906 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:26,917 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:27,917 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:28,916 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:29,924 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:30,934 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:31,957 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:32,965 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:33,950 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:34,965 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:35,994 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:37,002 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:38,026 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:39,036 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:40,036 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:41,032 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:42,028 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:43,013 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:44,019 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:45,018 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:46,031 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:47,043 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:48,046 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:49,058 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:50,056 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:51,063 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:52,068 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:53,067 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:54,075 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:55,100 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:56,109 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:57,119 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:58,129 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:36:59,128 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:00,125 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:01,110 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:02,124 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:03,116 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:04,122 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:05,123 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:06,135 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:07,128 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:08,137 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:09,143 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:10,158 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:11,155 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:11,831 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:37:11,832 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:37:12,168 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:13,163 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:14,171 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:15,184 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:16,184 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:17,208 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:18,222 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:19,237 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:20,245 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:21,252 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:22,253 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:23,239 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:24,251 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:25,252 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:26,252 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:27,250 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:28,252 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:29,251 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:30,252 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:31,250 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:32,251 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:33,252 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:34,251 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:35,253 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:36,251 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:37,252 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:38,251 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:39,251 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:40,251 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:41,251 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:42,252 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:43,252 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:44,251 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:45,252 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:46,252 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:47,250 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:48,251 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:49,252 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:50,252 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:51,253 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:52,240 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:53,240 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:54,241 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:55,241 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:56,242 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:57,240 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:58,238 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:37:59,252 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:38:00,242 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:38:01,240 - trading.live_trading_interface - ERROR - ❌ Error getting active users: no such table: user_sessions
2025-05-31 13:38:11,252 - database.models - INFO - [OK] Database migrations completed successfully
2025-05-31 13:38:11,252 - database.models - INFO - [OK] Database initialized successfully
2025-05-31 13:38:11,257 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:38:11,264 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:38:11,269 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:38:11,275 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:38:11,282 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:38:11,287 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:38:11,291 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:38:11,297 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:38:11,302 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:38:11,308 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:38:11,312 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:38:11,317 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:38:11,321 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:38:11,326 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:38:11,332 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:38:11,337 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:38:11,343 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:38:11,348 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:38:11,436 - market_data.advanced_market_data_manager - INFO - [DATA] Advanced Market Data Manager initialized
2025-05-31 13:38:11,437 - market_data.market_data_api - INFO - [API] Market Data API initialized
2025-05-31 13:38:11,437 - market_data.websocket_streamer - INFO - [WS] WebSocket Streamer initialized
2025-05-31 13:38:11,437 - notifications.notification_manager - INFO - [NOTIFY] Notification Manager initialized
2025-05-31 13:38:11,437 - trading.live_trading_interface - INFO - [TRADING] Live Trading Interface initialized
2025-05-31 13:38:11,437 - strategies.live_strategy_engine - INFO - [STRATEGY] Live Strategy Engine initialized
2025-05-31 13:38:11,437 - __main__ - INFO - [OK] Advanced trading components with real-time market data initialized
2025-05-31 13:38:11,437 - __main__ - INFO - [STARTUP] Money Circle initialized with config: development
2025-05-31 13:38:11,439 - market_data.market_data_api - INFO - [API] Market Data API routes configured
2025-05-31 13:38:11,439 - __main__ - INFO - [OK] Market Data API routes configured
2025-05-31 13:38:11,439 - __main__ - INFO - [OK] Market Data WebSocket routes configured
2025-05-31 13:38:11,439 - __main__ - INFO - [OK] Routes configured
2025-05-31 13:38:11,441 - __main__ - INFO - [OK] Web application configured
2025-05-31 13:38:11,445 - __main__ - INFO - [SERVER] Money Circle running at http://localhost:8080
2025-05-31 13:38:11,445 - __main__ - INFO - [READY] Platform ready for Money Circle investment club members
2025-05-31 13:38:11,445 - __main__ - INFO - [DATA] Advanced market data manager started
2025-05-31 13:38:11,445 - __main__ - INFO - 🌐 Market data WebSocket streamer subscribed to updates
2025-05-31 13:38:11,445 - __main__ - INFO - 📡 Notification system started
2025-05-31 13:38:11,445 - __main__ - INFO - ⚡ Live trading monitoring started
2025-05-31 13:38:11,445 - __main__ - INFO - 🤖 Strategy automation started
2025-05-31 13:38:11,445 - market_data.advanced_market_data_manager - INFO - [DATA] Starting advanced market data manager...
2025-05-31 13:38:11,445 - notifications.notification_manager - INFO - 📡 Starting notification delivery system...
2025-05-31 13:38:11,445 - trading.live_trading_interface - INFO - 📊 Starting real-time trading monitoring...
2025-05-31 13:38:11,446 - strategies.live_strategy_engine - INFO - 🚀 Starting live strategy automation...
2025-05-31 13:38:11,446 - market_data.advanced_market_data_manager - INFO - [DATA] Connecting to Binance WebSocket...
2025-05-31 13:38:11,463 - market_data.advanced_market_data_manager - INFO - [DATA] Connecting to HTX WebSocket...
2025-05-31 13:38:11,463 - market_data.advanced_market_data_manager - INFO - 🔄 Connecting to Bybit Spot WebSocket...
2025-05-31 13:38:11,471 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:38:11,471 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 0.0% (0 excellent, 0 good)
2025-05-31 13:38:11,471 - notifications.notification_manager - ERROR - ❌ Error cleaning up notifications: no such column: expires_at
2025-05-31 13:38:11,472 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:12,492 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:12,969 - market_data.advanced_market_data_manager - INFO - [DATA] Connected to Binance WebSocket
2025-05-31 13:38:13,020 - market_data.advanced_market_data_manager - INFO - ✅ Connected to Bybit Spot WebSocket
2025-05-31 13:38:13,021 - market_data.advanced_market_data_manager - INFO - 📡 Subscribed to Bybit tickers: ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
2025-05-31 13:38:13,149 - market_data.advanced_market_data_manager - INFO - [DATA] Connected to HTX WebSocket
2025-05-31 13:38:13,310 - market_data.advanced_market_data_manager - INFO - ✅ Bybit subscription confirmed: subscribe
2025-05-31 13:38:13,492 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:14,500 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:15,481 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:16,512 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:17,528 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:18,552 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:19,560 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:20,568 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:21,561 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:22,567 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:23,572 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:24,565 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:25,565 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:26,587 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:27,588 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:28,595 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:29,609 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:30,617 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:31,618 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:32,619 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:33,621 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:34,618 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:35,618 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:36,607 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:37,619 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:38,620 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:39,621 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:40,621 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:41,621 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:42,622 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:43,620 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:44,621 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:45,621 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:46,621 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:47,621 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:48,621 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:49,613 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:50,613 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:51,622 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:52,621 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:53,618 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:54,619 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:55,619 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:56,620 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:57,618 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:58,621 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:38:59,629 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:00,621 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:01,621 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:02,623 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:03,621 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:04,621 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:05,619 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:06,621 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:07,613 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:08,622 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:09,622 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:10,621 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:11,477 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:39:11,477 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:39:11,618 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:12,611 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:13,622 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:14,621 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:15,621 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:16,620 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:17,622 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:18,622 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:19,621 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:20,615 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:21,621 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:22,622 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:23,611 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:24,614 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:25,618 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:26,621 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:27,629 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:28,439 - aiohttp.access - INFO - ::1 [31/May/2025:12:39:28 -0600] "GET /register?error=server_error&username=dem292&email=<EMAIL> HTTP/1.1" 200 8807 "http://localhost:8080/register?error=server_error&username=dem29&email=<EMAIL>" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:39:28,621 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:29,633 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:30,622 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:31,622 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:32,613 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:33,615 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:34,104 - auth.user_manager - WARNING - [AUTH] Failed login attempt for: trader_alex
2025-05-31 13:39:34,104 - auth.user_manager - WARNING - [AUTH] Failed login attempt for: ::1
2025-05-31 13:39:34,105 - aiohttp.access - INFO - ::1 [31/May/2025:12:39:34 -0600] "POST /login HTTP/1.1" 302 318 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:39:34,128 - aiohttp.access - INFO - ::1 [31/May/2025:12:39:34 -0600] "GET /login?error=invalid_credentials HTTP/1.1" 200 17259 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:39:34,221 - aiohttp.access - INFO - ::1 [31/May/2025:12:39:34 -0600] "GET /static/js/common.js HTTP/1.1" 304 179 "http://localhost:8080/login?error=invalid_credentials" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:39:34,256 - aiohttp.access - INFO - ::1 [31/May/2025:12:39:34 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 172 "http://localhost:8080/login?error=invalid_credentials" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:39:34,621 - trading.live_trading_interface - ERROR - [ERROR] Error getting active users: no such column: expires_at
2025-05-31 13:39:45,064 - database.models - INFO - [MIGRATION] Adding expires_at column to user_sessions table
2025-05-31 13:39:45,068 - database.models - INFO - [OK] Database migrations completed successfully
2025-05-31 13:39:45,071 - database.models - INFO - [OK] Database initialized successfully
2025-05-31 13:39:45,077 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:39:45,083 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:39:45,088 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:39:45,093 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:39:45,099 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:39:45,105 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:39:45,109 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:39:45,113 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:39:45,119 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:39:45,125 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:39:45,130 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:39:45,135 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:39:45,141 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:39:45,146 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:39:45,154 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:39:45,159 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:39:45,166 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:39:45,172 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:39:45,258 - market_data.advanced_market_data_manager - INFO - [DATA] Advanced Market Data Manager initialized
2025-05-31 13:39:45,258 - market_data.market_data_api - INFO - [API] Market Data API initialized
2025-05-31 13:39:45,258 - market_data.websocket_streamer - INFO - [WS] WebSocket Streamer initialized
2025-05-31 13:39:45,258 - notifications.notification_manager - INFO - [NOTIFY] Notification Manager initialized
2025-05-31 13:39:45,258 - trading.live_trading_interface - INFO - [TRADING] Live Trading Interface initialized
2025-05-31 13:39:45,258 - strategies.live_strategy_engine - INFO - [STRATEGY] Live Strategy Engine initialized
2025-05-31 13:39:45,258 - __main__ - INFO - [OK] Advanced trading components with real-time market data initialized
2025-05-31 13:39:45,258 - __main__ - INFO - [STARTUP] Money Circle initialized with config: development
2025-05-31 13:39:45,261 - market_data.market_data_api - INFO - [API] Market Data API routes configured
2025-05-31 13:39:45,261 - __main__ - INFO - [OK] Market Data API routes configured
2025-05-31 13:39:45,261 - __main__ - INFO - [OK] Market Data WebSocket routes configured
2025-05-31 13:39:45,262 - __main__ - INFO - [OK] Routes configured
2025-05-31 13:39:45,262 - __main__ - INFO - [OK] Web application configured
2025-05-31 13:39:45,267 - __main__ - INFO - [SERVER] Money Circle running at http://localhost:8080
2025-05-31 13:39:45,268 - __main__ - INFO - [READY] Platform ready for Money Circle investment club members
2025-05-31 13:39:45,268 - __main__ - INFO - [DATA] Advanced market data manager started
2025-05-31 13:39:45,268 - __main__ - INFO - 🌐 Market data WebSocket streamer subscribed to updates
2025-05-31 13:39:45,268 - __main__ - INFO - 📡 Notification system started
2025-05-31 13:39:45,268 - __main__ - INFO - ⚡ Live trading monitoring started
2025-05-31 13:39:45,268 - __main__ - INFO - 🤖 Strategy automation started
2025-05-31 13:39:45,268 - market_data.advanced_market_data_manager - INFO - [DATA] Starting advanced market data manager...
2025-05-31 13:39:45,268 - notifications.notification_manager - INFO - 📡 Starting notification delivery system...
2025-05-31 13:39:45,269 - trading.live_trading_interface - INFO - 📊 Starting real-time trading monitoring...
2025-05-31 13:39:45,269 - strategies.live_strategy_engine - INFO - 🚀 Starting live strategy automation...
2025-05-31 13:39:45,269 - market_data.advanced_market_data_manager - INFO - [DATA] Connecting to Binance WebSocket...
2025-05-31 13:39:45,287 - market_data.advanced_market_data_manager - INFO - [DATA] Connecting to HTX WebSocket...
2025-05-31 13:39:45,337 - market_data.advanced_market_data_manager - INFO - 🔄 Connecting to Bybit Spot WebSocket...
2025-05-31 13:39:45,350 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:39:45,350 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 0.0% (0 excellent, 0 good)
2025-05-31 13:39:45,351 - notifications.notification_manager - ERROR - ❌ Error cleaning up notifications: no such column: expires_at
2025-05-31 13:39:46,789 - market_data.advanced_market_data_manager - INFO - [DATA] Connected to Binance WebSocket
2025-05-31 13:39:46,845 - market_data.advanced_market_data_manager - INFO - ✅ Connected to Bybit Spot WebSocket
2025-05-31 13:39:46,845 - market_data.advanced_market_data_manager - INFO - 📡 Subscribed to Bybit tickers: ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
2025-05-31 13:39:46,962 - market_data.advanced_market_data_manager - INFO - [DATA] Connected to HTX WebSocket
2025-05-31 13:39:47,135 - market_data.advanced_market_data_manager - INFO - ✅ Bybit subscription confirmed: subscribe
2025-05-31 13:39:48,093 - aiohttp.access - INFO - ::1 [31/May/2025:12:39:48 -0600] "GET /login?error=invalid_credentials HTTP/1.1" 200 17259 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:39:48,159 - aiohttp.access - INFO - ::1 [31/May/2025:12:39:48 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 172 "http://localhost:8080/login?error=invalid_credentials" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:39:48,921 - aiohttp.access - INFO - ::1 [31/May/2025:12:39:48 -0600] "GET /login?error=invalid_credentials HTTP/1.1" 200 17259 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:39:49,003 - aiohttp.access - INFO - ::1 [31/May/2025:12:39:49 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 172 "http://localhost:8080/login?error=invalid_credentials" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:39:50,952 - auth.user_manager - WARNING - [AUTH] Failed login attempt for: trader_alex
2025-05-31 13:39:50,952 - auth.user_manager - WARNING - [AUTH] Failed login attempt for: ::1
2025-05-31 13:39:50,953 - aiohttp.access - INFO - ::1 [31/May/2025:12:39:50 -0600] "POST /login HTTP/1.1" 302 318 "http://localhost:8080/login?error=invalid_credentials" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:39:50,956 - aiohttp.access - INFO - ::1 [31/May/2025:12:39:50 -0600] "GET /login?error=invalid_credentials HTTP/1.1" 200 17259 "http://localhost:8080/login?error=invalid_credentials" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:39:51,075 - aiohttp.access - INFO - ::1 [31/May/2025:12:39:51 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 172 "http://localhost:8080/login?error=invalid_credentials" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:40:45,358 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:40:45,358 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:41:49,790 - database.models - INFO - [OK] Database migrations completed successfully
2025-05-31 13:41:49,790 - database.models - INFO - [OK] Database initialized successfully
2025-05-31 13:41:49,799 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:41:49,806 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:41:49,812 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:41:49,819 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:41:49,826 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:41:49,833 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:41:49,841 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:41:49,848 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:41:49,854 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:41:49,859 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:41:49,865 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:41:49,871 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:41:49,876 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:41:49,882 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:41:49,888 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:41:49,894 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:41:49,900 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:41:49,906 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:41:49,983 - market_data.advanced_market_data_manager - INFO - [DATA] Advanced Market Data Manager initialized
2025-05-31 13:41:49,983 - market_data.market_data_api - INFO - [API] Market Data API initialized
2025-05-31 13:41:49,983 - market_data.websocket_streamer - INFO - [WS] WebSocket Streamer initialized
2025-05-31 13:41:49,983 - notifications.notification_manager - INFO - [NOTIFY] Notification Manager initialized
2025-05-31 13:41:49,983 - trading.live_trading_interface - INFO - [TRADING] Live Trading Interface initialized
2025-05-31 13:41:49,983 - strategies.live_strategy_engine - INFO - [STRATEGY] Live Strategy Engine initialized
2025-05-31 13:41:49,983 - __main__ - INFO - [OK] Advanced trading components with real-time market data initialized
2025-05-31 13:41:49,983 - __main__ - INFO - [STARTUP] Money Circle initialized with config: development
2025-05-31 13:41:49,986 - market_data.market_data_api - INFO - [API] Market Data API routes configured
2025-05-31 13:41:49,987 - __main__ - INFO - [OK] Market Data API routes configured
2025-05-31 13:41:49,987 - __main__ - INFO - [OK] Market Data WebSocket routes configured
2025-05-31 13:41:49,987 - __main__ - INFO - [OK] Routes configured
2025-05-31 13:41:49,988 - __main__ - INFO - [OK] Web application configured
2025-05-31 13:41:49,993 - __main__ - INFO - [SERVER] Money Circle running at http://localhost:8080
2025-05-31 13:41:49,993 - __main__ - INFO - [READY] Platform ready for Money Circle investment club members
2025-05-31 13:41:49,994 - __main__ - INFO - [DATA] Advanced market data manager started
2025-05-31 13:41:49,994 - __main__ - INFO - 🌐 Market data WebSocket streamer subscribed to updates
2025-05-31 13:41:49,994 - __main__ - INFO - 📡 Notification system started
2025-05-31 13:41:49,994 - __main__ - INFO - ⚡ Live trading monitoring started
2025-05-31 13:41:49,994 - __main__ - INFO - 🤖 Strategy automation started
2025-05-31 13:41:49,994 - market_data.advanced_market_data_manager - INFO - [DATA] Starting advanced market data manager...
2025-05-31 13:41:49,994 - notifications.notification_manager - INFO - 📡 Starting notification delivery system...
2025-05-31 13:41:49,994 - trading.live_trading_interface - INFO - 📊 Starting real-time trading monitoring...
2025-05-31 13:41:49,994 - strategies.live_strategy_engine - INFO - 🚀 Starting live strategy automation...
2025-05-31 13:41:49,994 - market_data.advanced_market_data_manager - INFO - [DATA] Connecting to Binance WebSocket...
2025-05-31 13:41:50,013 - market_data.advanced_market_data_manager - INFO - [DATA] Connecting to HTX WebSocket...
2025-05-31 13:41:50,014 - market_data.advanced_market_data_manager - INFO - 🔄 Connecting to Bybit Spot WebSocket...
2025-05-31 13:41:50,022 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:41:50,023 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 0.0% (0 excellent, 0 good)
2025-05-31 13:41:50,023 - notifications.notification_manager - ERROR - ❌ Error cleaning up notifications: no such column: expires_at
2025-05-31 13:41:51,496 - market_data.advanced_market_data_manager - INFO - [DATA] Connected to Binance WebSocket
2025-05-31 13:41:51,545 - market_data.advanced_market_data_manager - INFO - ✅ Connected to Bybit Spot WebSocket
2025-05-31 13:41:51,546 - market_data.advanced_market_data_manager - INFO - 📡 Subscribed to Bybit tickers: ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
2025-05-31 13:41:51,625 - market_data.advanced_market_data_manager - INFO - [DATA] Connected to HTX WebSocket
2025-05-31 13:41:51,835 - market_data.advanced_market_data_manager - INFO - ✅ Bybit subscription confirmed: subscribe
2025-05-31 13:41:56,256 - aiohttp.access - INFO - ::1 [31/May/2025:12:41:56 -0600] "GET /login?error=invalid_credentials HTTP/1.1" 200 17259 "http://localhost:8080/login?error=invalid_credentials" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:41:56,273 - aiohttp.access - INFO - ::1 [31/May/2025:12:41:56 -0600] "GET /static/css/design_system.css HTTP/1.1" 304 179 "http://localhost:8080/login?error=invalid_credentials" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:41:56,275 - aiohttp.access - INFO - ::1 [31/May/2025:12:41:56 -0600] "GET /static/css/unified_footer.css HTTP/1.1" 304 179 "http://localhost:8080/login?error=invalid_credentials" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:41:56,275 - aiohttp.access - INFO - ::1 [31/May/2025:12:41:56 -0600] "GET /static/css/unified_header.css HTTP/1.1" 304 179 "http://localhost:8080/login?error=invalid_credentials" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:41:56,361 - aiohttp.access - INFO - ::1 [31/May/2025:12:41:56 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 172 "http://localhost:8080/login?error=invalid_credentials" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:41:58,173 - auth.user_manager - WARNING - [AUTH] Failed login attempt for: trader_alex
2025-05-31 13:41:58,173 - auth.user_manager - WARNING - [AUTH] Failed login attempt for: ::1
2025-05-31 13:41:58,175 - aiohttp.access - INFO - ::1 [31/May/2025:12:41:58 -0600] "POST /login HTTP/1.1" 302 318 "http://localhost:8080/login?error=invalid_credentials" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:41:58,178 - aiohttp.access - INFO - ::1 [31/May/2025:12:41:58 -0600] "GET /login?error=invalid_credentials HTTP/1.1" 200 17259 "http://localhost:8080/login?error=invalid_credentials" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:41:58,312 - aiohttp.access - INFO - ::1 [31/May/2025:12:41:58 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 172 "http://localhost:8080/login?error=invalid_credentials" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:42:02,889 - auth.user_manager - WARNING - [AUTH] Failed login attempt for: crypto_sarah
2025-05-31 13:42:02,890 - auth.user_manager - WARNING - [AUTH] Failed login attempt for: ::1
2025-05-31 13:42:02,891 - aiohttp.access - INFO - ::1 [31/May/2025:12:42:02 -0600] "POST /login HTTP/1.1" 302 318 "http://localhost:8080/login?error=invalid_credentials" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:42:02,895 - aiohttp.access - INFO - ::1 [31/May/2025:12:42:02 -0600] "GET /login?error=invalid_credentials HTTP/1.1" 200 17259 "http://localhost:8080/login?error=invalid_credentials" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:42:02,997 - aiohttp.access - INFO - ::1 [31/May/2025:12:42:02 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 172 "http://localhost:8080/login?error=invalid_credentials" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:42:10,580 - auth.user_manager - INFO - [AUTH] Successful login: epinnox from ::1
2025-05-31 13:42:10,580 - auth.user_manager - INFO - [AUTH] Session created for user: epinnox
2025-05-31 13:42:10,580 - __main__ - INFO - [OK] User logged in: epinnox
2025-05-31 13:42:10,581 - aiohttp.access - INFO - ::1 [31/May/2025:12:42:10 -0600] "POST /login HTTP/1.1" 302 408 "http://localhost:8080/login?error=invalid_credentials" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:42:29,450 - aiohttp.access - INFO - ::1 [31/May/2025:12:42:29 -0600] "GET /login HTTP/1.1" 200 17109 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:42:29,450 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:42:29,451 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:42:29,451 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:42:29,451 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:42:29,451 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:42:29,452 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:42:29,452 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:42:29,452 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:42:29,452 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:42:29,452 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:42:29,452 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:42:29,453 - market_data.advanced_market_data_manager - WARNING - ⚠️ HTX WebSocket connection closed: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:42:29,453 - market_data.advanced_market_data_manager - INFO - 🔄 Reconnecting to HTX WebSocket in 5 seconds...
2025-05-31 13:42:29,453 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 0 tables
2025-05-31 13:42:29,476 - aiohttp.server - ERROR - Error handling request
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_protocol.py", line 477, in _handle_request
    resp = await request_handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_app.py", line 567, in _handle
    return await handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_middlewares.py", line 117, in impl
    return await handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_session\__init__.py", line 191, in factory
    response = await handler(request)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\app.py", line 206, in auth_middleware
    return await handler(request)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\dashboards\personal_dashboard.py", line 60, in serve_personal_dashboard
    return aiohttp_jinja2.render_template('personal_dashboard.html', request, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_jinja2\__init__.py", line 176, in render_template
    response.text = render_string(template_name, request, context, app_key=app_key)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_jinja2\__init__.py", line 139, in render_string
    return template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\personal_dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\base.html", line 40, in top-level template code
    {% include 'components/header.html' %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\components\header.html", line 15, in top-level template code
    <a href="/dashboard" class="nav-link {% if request.path == '/dashboard' or request.path == '/personal' %}active{% endif %}">
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
jinja2.exceptions.UndefinedError: 'request' is undefined
2025-05-31 13:42:29,479 - aiohttp.access - INFO - ::1 [31/May/2025:12:42:10 -0600] "GET /dashboard HTTP/1.1" 500 336 "http://localhost:8080/login?error=invalid_credentials" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:42:29,773 - aiohttp.access - INFO - ::1 [31/May/2025:12:42:29 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 172 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:42:34,463 - market_data.advanced_market_data_manager - INFO - [DATA] Connecting to HTX WebSocket...
2025-05-31 13:42:35,022 - auth.user_manager - INFO - [AUTH] Successful login: epinnox from ::1
2025-05-31 13:42:35,022 - auth.user_manager - INFO - [AUTH] Session created for user: epinnox
2025-05-31 13:42:35,022 - __main__ - INFO - [OK] User logged in: epinnox
2025-05-31 13:42:35,023 - aiohttp.access - INFO - ::1 [31/May/2025:12:42:34 -0600] "POST /login HTTP/1.1" 302 408 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:42:51,639 - market_data.advanced_market_data_manager - ERROR - ❌ HTX WebSocket error: timed out during opening handshake
2025-05-31 13:42:51,640 - market_data.advanced_market_data_manager - INFO - 🔄 Reconnecting to HTX WebSocket in 5 seconds...
2025-05-31 13:42:51,640 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:42:51,640 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:42:51,641 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 0 tables
2025-05-31 13:42:51,655 - aiohttp.server - ERROR - Error handling request
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_protocol.py", line 477, in _handle_request
    resp = await request_handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_app.py", line 567, in _handle
    return await handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_middlewares.py", line 117, in impl
    return await handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_session\__init__.py", line 191, in factory
    response = await handler(request)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\app.py", line 206, in auth_middleware
    return await handler(request)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\dashboards\personal_dashboard.py", line 60, in serve_personal_dashboard
    return aiohttp_jinja2.render_template('personal_dashboard.html', request, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_jinja2\__init__.py", line 176, in render_template
    response.text = render_string(template_name, request, context, app_key=app_key)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_jinja2\__init__.py", line 139, in render_string
    return template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\personal_dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\base.html", line 40, in top-level template code
    {% include 'components/header.html' %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\components\header.html", line 15, in top-level template code
    <a href="/dashboard" class="nav-link {% if request.path == '/dashboard' or request.path == '/personal' %}active{% endif %}">
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
jinja2.exceptions.UndefinedError: 'request' is undefined
2025-05-31 13:42:51,657 - aiohttp.access - INFO - ::1 [31/May/2025:12:42:35 -0600] "GET /dashboard HTTP/1.1" 500 336 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:42:52,123 - auth.user_manager - INFO - [AUTH] Successful login: epinnox from ::1
2025-05-31 13:42:52,125 - auth.user_manager - INFO - [AUTH] Session created for user: epinnox
2025-05-31 13:42:52,125 - __main__ - INFO - [OK] User logged in: epinnox
2025-05-31 13:42:52,125 - aiohttp.access - INFO - ::1 [31/May/2025:12:42:51 -0600] "POST /login HTTP/1.1" 302 237 "-" "python-requests/2.32.3"
2025-05-31 13:42:56,643 - market_data.advanced_market_data_manager - INFO - [DATA] Connecting to HTX WebSocket...
2025-05-31 13:42:58,155 - market_data.advanced_market_data_manager - INFO - [DATA] Connected to HTX WebSocket
2025-05-31 13:43:25,071 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:43:25,071 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:43:25,071 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:43:25,072 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:43:25,072 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:43:25,072 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:43:25,072 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:43:25,073 - market_data.advanced_market_data_manager - ERROR - ❌ HTX WebSocket error: 'NoneType' object has no attribute 'resume_reading'
2025-05-31 13:43:25,073 - market_data.advanced_market_data_manager - INFO - 🔄 Reconnecting to HTX WebSocket in 5 seconds...
2025-05-31 13:43:25,073 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 0 tables
2025-05-31 13:43:25,084 - aiohttp.server - ERROR - Error handling request
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_protocol.py", line 477, in _handle_request
    resp = await request_handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_app.py", line 567, in _handle
    return await handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_middlewares.py", line 117, in impl
    return await handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_session\__init__.py", line 191, in factory
    response = await handler(request)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\app.py", line 206, in auth_middleware
    return await handler(request)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\dashboards\personal_dashboard.py", line 60, in serve_personal_dashboard
    return aiohttp_jinja2.render_template('personal_dashboard.html', request, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_jinja2\__init__.py", line 176, in render_template
    response.text = render_string(template_name, request, context, app_key=app_key)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_jinja2\__init__.py", line 139, in render_string
    return template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\personal_dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\base.html", line 40, in top-level template code
    {% include 'components/header.html' %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\components\header.html", line 15, in top-level template code
    <a href="/dashboard" class="nav-link {% if request.path == '/dashboard' or request.path == '/personal' %}active{% endif %}">
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
jinja2.exceptions.UndefinedError: 'request' is undefined
2025-05-31 13:43:25,085 - aiohttp.access - INFO - ::1 [31/May/2025:12:43:07 -0600] "GET /dashboard HTTP/1.1" 500 336 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:43:29,452 - market_data.advanced_market_data_manager - WARNING - ⚠️ htx connection is down
2025-05-31 13:43:30,075 - market_data.advanced_market_data_manager - INFO - [DATA] Connecting to HTX WebSocket...
2025-05-31 13:43:31,569 - market_data.advanced_market_data_manager - INFO - [DATA] Connected to HTX WebSocket
2025-05-31 13:43:51,641 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:43:51,641 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:44:51,653 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:44:51,654 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:45:51,665 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:45:51,665 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:52:56,196 - database.models - INFO - [OK] Database migrations completed successfully
2025-05-31 13:52:56,197 - database.models - INFO - [OK] Database initialized successfully
2025-05-31 13:52:56,205 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:52:56,210 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:52:56,215 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:52:56,219 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:52:56,223 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:52:56,228 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:52:56,233 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:52:56,238 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:52:56,243 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:52:56,248 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:52:56,253 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:52:56,258 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:52:56,263 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:52:56,268 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:52:56,273 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:52:56,278 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:52:56,282 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:52:56,288 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:52:56,364 - market_data.advanced_market_data_manager - INFO - [DATA] Advanced Market Data Manager initialized
2025-05-31 13:52:56,364 - market_data.market_data_api - INFO - [API] Market Data API initialized
2025-05-31 13:52:56,364 - market_data.websocket_streamer - INFO - [WS] WebSocket Streamer initialized
2025-05-31 13:52:56,364 - notifications.notification_manager - INFO - [NOTIFY] Notification Manager initialized
2025-05-31 13:52:56,364 - trading.live_trading_interface - INFO - [TRADING] Live Trading Interface initialized
2025-05-31 13:52:56,364 - strategies.live_strategy_engine - INFO - [STRATEGY] Live Strategy Engine initialized
2025-05-31 13:52:56,364 - __main__ - INFO - [OK] Advanced trading components with real-time market data initialized
2025-05-31 13:52:56,366 - __main__ - INFO - [STARTUP] Money Circle initialized with config: development
2025-05-31 13:52:56,368 - market_data.market_data_api - INFO - [API] Market Data API routes configured
2025-05-31 13:52:56,368 - __main__ - INFO - [OK] Market Data API routes configured
2025-05-31 13:52:56,368 - __main__ - INFO - [OK] Market Data WebSocket routes configured
2025-05-31 13:52:56,368 - __main__ - INFO - [OK] Routes configured
2025-05-31 13:52:56,369 - __main__ - INFO - [OK] Web application configured
2025-05-31 13:53:49,820 - database.models - INFO - [OK] Database migrations completed successfully
2025-05-31 13:53:49,820 - database.models - INFO - [OK] Database initialized successfully
2025-05-31 13:53:49,828 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:53:49,834 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:53:49,839 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:53:49,843 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:53:49,849 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:53:49,854 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:53:49,858 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:53:49,862 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:53:49,867 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:53:49,871 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:53:49,876 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:53:49,883 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:53:49,890 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:53:49,897 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:53:49,904 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:53:49,909 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:53:49,913 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:53:49,919 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:53:49,998 - market_data.advanced_market_data_manager - INFO - [DATA] Advanced Market Data Manager initialized
2025-05-31 13:53:49,998 - market_data.market_data_api - INFO - [API] Market Data API initialized
2025-05-31 13:53:49,998 - market_data.websocket_streamer - INFO - [WS] WebSocket Streamer initialized
2025-05-31 13:53:49,998 - notifications.notification_manager - INFO - [NOTIFY] Notification Manager initialized
2025-05-31 13:53:49,998 - trading.live_trading_interface - INFO - [TRADING] Live Trading Interface initialized
2025-05-31 13:53:49,998 - strategies.live_strategy_engine - INFO - [STRATEGY] Live Strategy Engine initialized
2025-05-31 13:53:49,998 - __main__ - INFO - [OK] Advanced trading components with real-time market data initialized
2025-05-31 13:53:49,998 - __main__ - INFO - [STARTUP] Money Circle initialized with config: development
2025-05-31 13:53:50,000 - market_data.market_data_api - INFO - [API] Market Data API routes configured
2025-05-31 13:53:50,000 - __main__ - INFO - [OK] Market Data API routes configured
2025-05-31 13:53:50,000 - __main__ - INFO - [OK] Market Data WebSocket routes configured
2025-05-31 13:53:50,001 - __main__ - INFO - [OK] Routes configured
2025-05-31 13:53:50,002 - __main__ - INFO - [OK] Web application configured
2025-05-31 13:53:50,007 - __main__ - INFO - [SERVER] Money Circle running at http://localhost:8085
2025-05-31 13:53:50,008 - __main__ - INFO - [READY] Platform ready for Money Circle investment club members
2025-05-31 13:53:50,008 - __main__ - INFO - [DATA] Advanced market data manager started
2025-05-31 13:53:50,008 - __main__ - INFO - [WS] Market data WebSocket streamer subscribed to updates
2025-05-31 13:53:50,008 - __main__ - INFO - [NOTIFY] Notification system started
2025-05-31 13:53:50,008 - __main__ - INFO - [TRADING] Live trading monitoring started
2025-05-31 13:53:50,008 - __main__ - INFO - [STRATEGY] Strategy automation started
2025-05-31 13:53:50,008 - market_data.advanced_market_data_manager - INFO - [DATA] Starting advanced market data manager...
2025-05-31 13:53:50,008 - notifications.notification_manager - INFO - [NOTIFY] Starting notification delivery system...
2025-05-31 13:53:50,008 - trading.live_trading_interface - INFO - [TRADING] Starting real-time trading monitoring...
2025-05-31 13:53:50,008 - strategies.live_strategy_engine - INFO - [STRATEGY] Starting live strategy automation...
2025-05-31 13:53:50,009 - market_data.advanced_market_data_manager - INFO - [DATA] Connecting to Binance WebSocket...
2025-05-31 13:53:50,027 - market_data.advanced_market_data_manager - INFO - [DATA] Connecting to HTX WebSocket...
2025-05-31 13:53:50,028 - market_data.advanced_market_data_manager - INFO - 🔄 Connecting to Bybit Spot WebSocket...
2025-05-31 13:53:50,036 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:53:50,036 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 0.0% (0 excellent, 0 good)
2025-05-31 13:53:51,507 - market_data.advanced_market_data_manager - INFO - [DATA] Connected to Binance WebSocket
2025-05-31 13:53:51,632 - market_data.advanced_market_data_manager - INFO - [DATA] Connected to HTX WebSocket
2025-05-31 13:53:51,802 - market_data.advanced_market_data_manager - INFO - ✅ Connected to Bybit Spot WebSocket
2025-05-31 13:53:51,803 - market_data.advanced_market_data_manager - INFO - 📡 Subscribed to Bybit tickers: ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
2025-05-31 13:53:52,110 - market_data.advanced_market_data_manager - INFO - ✅ Bybit subscription confirmed: subscribe
2025-05-31 13:54:29,241 - aiohttp.access - INFO - ::1 [31/May/2025:12:54:29 -0600] "GET / HTTP/1.1" 302 132 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:54:29,255 - aiohttp.access - INFO - ::1 [31/May/2025:12:54:29 -0600] "GET /login HTTP/1.1" 200 17085 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:54:29,276 - aiohttp.access - INFO - ::1 [31/May/2025:12:54:29 -0600] "GET /static/css/design_system.css HTTP/1.1" 200 237 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:54:29,281 - aiohttp.access - INFO - ::1 [31/May/2025:12:54:29 -0600] "GET /static/css/unified_header.css HTTP/1.1" 200 237 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:54:29,283 - aiohttp.access - INFO - ::1 [31/May/2025:12:54:29 -0600] "GET /static/css/unified_footer.css HTTP/1.1" 200 237 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:54:29,283 - aiohttp.access - INFO - ::1 [31/May/2025:12:54:29 -0600] "GET /static/css/auth.css HTTP/1.1" 200 236 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:54:29,284 - aiohttp.access - INFO - ::1 [31/May/2025:12:54:29 -0600] "GET /static/js/common.js HTTP/1.1" 200 252 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:54:29,526 - aiohttp.access - INFO - ::1 [31/May/2025:12:54:29 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 172 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:54:29,547 - aiohttp.access - INFO - ::1 [31/May/2025:12:54:29 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 172 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:54:31,808 - auth.user_manager - WARNING - [AUTH] Failed login attempt for: trader_alex
2025-05-31 13:54:31,809 - auth.user_manager - WARNING - [AUTH] Failed login attempt for: ::1
2025-05-31 13:54:31,810 - aiohttp.access - INFO - ::1 [31/May/2025:12:54:31 -0600] "POST /login HTTP/1.1" 302 318 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:54:31,813 - aiohttp.access - INFO - ::1 [31/May/2025:12:54:31 -0600] "GET /login?error=invalid_credentials HTTP/1.1" 200 17235 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:54:31,935 - aiohttp.access - INFO - ::1 [31/May/2025:12:54:31 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 172 "http://localhost:8085/login?error=invalid_credentials" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:54:37,087 - auth.user_manager - INFO - [AUTH] Successful login: epinnox from ::1
2025-05-31 13:54:37,087 - auth.user_manager - INFO - [AUTH] Session created for user: epinnox
2025-05-31 13:54:37,088 - __main__ - INFO - [OK] User logged in: epinnox
2025-05-31 13:54:37,088 - aiohttp.access - INFO - ::1 [31/May/2025:12:54:36 -0600] "POST /login HTTP/1.1" 302 408 "http://localhost:8085/login?error=invalid_credentials" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:54:56,027 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:54:56,027 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:54:56,198 - auth.user_manager - INFO - [AUTH] Successful login: epinnox from ::1
2025-05-31 13:54:56,198 - auth.user_manager - INFO - [AUTH] Session created for user: epinnox
2025-05-31 13:54:56,198 - __main__ - INFO - [OK] User logged in: epinnox
2025-05-31 13:54:56,198 - aiohttp.access - INFO - ::1 [31/May/2025:12:54:56 -0600] "POST /login HTTP/1.1" 302 0 "-" "python-requests/2.32.3"
2025-05-31 13:54:56,199 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:54:56,199 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:54:56,199 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:54:56,199 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:54:56,200 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:54:56,200 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:54:56,200 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:54:56,200 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:54:56,200 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:54:56,200 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:54:56,200 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:54:56,201 - market_data.advanced_market_data_manager - WARNING - ⚠️ HTX WebSocket connection closed: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:54:56,201 - market_data.advanced_market_data_manager - INFO - 🔄 Reconnecting to HTX WebSocket in 5 seconds...
2025-05-31 13:54:56,201 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 0 tables
2025-05-31 13:54:56,227 - aiohttp.server - ERROR - Error handling request
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_protocol.py", line 477, in _handle_request
    resp = await request_handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_app.py", line 567, in _handle
    return await handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_middlewares.py", line 117, in impl
    return await handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_session\__init__.py", line 191, in factory
    response = await handler(request)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\app.py", line 206, in auth_middleware
    return await handler(request)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\dashboards\personal_dashboard.py", line 61, in serve_personal_dashboard
    return aiohttp_jinja2.render_template('personal_dashboard.html', request, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_jinja2\__init__.py", line 176, in render_template
    response.text = render_string(template_name, request, context, app_key=app_key)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_jinja2\__init__.py", line 139, in render_string
    return template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\personal_dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\base.html", line 54, in top-level template code
    {% block extra_js %}{% endblock %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\personal_dashboard.html", line 249, in block 'extra_js'
    exchanges: {{ exchanges|tojson }}
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\filters.py", line 1721, in do_tojson
    return htmlsafe_json_dumps(value, dumps=dumps, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\utils.py", line 669, in htmlsafe_json_dumps
    dumps(obj, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\json\__init__.py", line 234, in dumps
    return cls(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type UserExchange is not JSON serializable
2025-05-31 13:54:56,254 - aiohttp.access - INFO - ::1 [31/May/2025:12:54:37 -0600] "GET /dashboard HTTP/1.1" 500 336 "http://localhost:8085/login?error=invalid_credentials" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:54:56,357 - aiohttp.access - INFO - ::1 [31/May/2025:12:54:56 -0600] "GET /favicon.ico HTTP/1.1" 404 174 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:55:00,085 - aiohttp.access - INFO - ::1 [31/May/2025:12:55:00 -0600] "GET /login HTTP/1.1" 200 17085 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:55:00,400 - aiohttp.access - INFO - ::1 [31/May/2025:12:55:00 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 172 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:55:01,217 - market_data.advanced_market_data_manager - INFO - [DATA] Connecting to HTX WebSocket...
2025-05-31 13:55:02,730 - market_data.advanced_market_data_manager - INFO - [DATA] Connected to HTX WebSocket
2025-05-31 13:55:05,411 - auth.user_manager - INFO - [AUTH] Successful login: epinnox from ::1
2025-05-31 13:55:05,412 - auth.user_manager - INFO - [AUTH] Session created for user: epinnox
2025-05-31 13:55:05,412 - __main__ - INFO - [OK] User logged in: epinnox
2025-05-31 13:55:05,413 - aiohttp.access - INFO - ::1 [31/May/2025:12:55:05 -0600] "POST /login HTTP/1.1" 302 408 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:55:23,505 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:55:23,506 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:55:23,506 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:55:23,507 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:55:23,507 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:55:23,507 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:55:23,507 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:55:23,508 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:55:23,508 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:55:23,508 - market_data.advanced_market_data_manager - ERROR - ❌ Error processing HTX data: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:55:23,509 - market_data.advanced_market_data_manager - WARNING - ⚠️ HTX WebSocket connection closed: received 1003 (unsupported data) ping check expired; then sent 1003 (unsupported data) ping check expired
2025-05-31 13:55:23,509 - market_data.advanced_market_data_manager - INFO - 🔄 Reconnecting to HTX WebSocket in 5 seconds...
2025-05-31 13:55:23,509 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 0 tables
2025-05-31 13:55:23,523 - aiohttp.server - ERROR - Error handling request
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_protocol.py", line 477, in _handle_request
    resp = await request_handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_app.py", line 567, in _handle
    return await handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_middlewares.py", line 117, in impl
    return await handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_session\__init__.py", line 191, in factory
    response = await handler(request)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\app.py", line 206, in auth_middleware
    return await handler(request)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\dashboards\personal_dashboard.py", line 61, in serve_personal_dashboard
    return aiohttp_jinja2.render_template('personal_dashboard.html', request, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_jinja2\__init__.py", line 176, in render_template
    response.text = render_string(template_name, request, context, app_key=app_key)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_jinja2\__init__.py", line 139, in render_string
    return template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\personal_dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\base.html", line 54, in top-level template code
    {% block extra_js %}{% endblock %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\personal_dashboard.html", line 249, in block 'extra_js'
    exchanges: {{ exchanges|tojson }}
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\filters.py", line 1721, in do_tojson
    return htmlsafe_json_dumps(value, dumps=dumps, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\utils.py", line 669, in htmlsafe_json_dumps
    dumps(obj, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\json\__init__.py", line 234, in dumps
    return cls(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type UserExchange is not JSON serializable
2025-05-31 13:55:23,524 - aiohttp.access - INFO - ::1 [31/May/2025:12:55:05 -0600] "GET /dashboard HTTP/1.1" 500 336 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:55:26,019 - market_data.advanced_market_data_manager - WARNING - ⚠️ htx connection is down
2025-05-31 13:55:28,521 - market_data.advanced_market_data_manager - INFO - [DATA] Connecting to HTX WebSocket...
2025-05-31 13:55:29,883 - market_data.advanced_market_data_manager - INFO - [DATA] Connected to HTX WebSocket
2025-05-31 13:56:01,794 - database.models - INFO - [OK] Database migrations completed successfully
2025-05-31 13:56:01,795 - database.models - INFO - [OK] Database initialized successfully
2025-05-31 13:56:01,799 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:56:01,805 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:56:01,809 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:56:01,815 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:56:01,821 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:56:01,825 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:56:01,833 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:56:01,839 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:56:01,843 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:56:01,850 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:56:01,855 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:56:01,861 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:56:01,866 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:56:01,872 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:56:01,877 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:56:01,883 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:56:01,889 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:56:01,895 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:56:01,971 - market_data.advanced_market_data_manager - INFO - [DATA] Advanced Market Data Manager initialized
2025-05-31 13:56:01,971 - market_data.market_data_api - INFO - [API] Market Data API initialized
2025-05-31 13:56:01,971 - market_data.websocket_streamer - INFO - [WS] WebSocket Streamer initialized
2025-05-31 13:56:01,972 - notifications.notification_manager - INFO - [NOTIFY] Notification Manager initialized
2025-05-31 13:56:01,972 - trading.live_trading_interface - INFO - [TRADING] Live Trading Interface initialized
2025-05-31 13:56:01,972 - strategies.live_strategy_engine - INFO - [STRATEGY] Live Strategy Engine initialized
2025-05-31 13:56:01,972 - __main__ - INFO - [OK] Advanced trading components with real-time market data initialized
2025-05-31 13:56:01,972 - __main__ - INFO - [STARTUP] Money Circle initialized with config: development
2025-05-31 13:56:01,973 - market_data.market_data_api - INFO - [API] Market Data API routes configured
2025-05-31 13:56:01,975 - __main__ - INFO - [OK] Market Data API routes configured
2025-05-31 13:56:01,975 - __main__ - INFO - [OK] Market Data WebSocket routes configured
2025-05-31 13:56:01,975 - __main__ - INFO - [OK] Routes configured
2025-05-31 13:56:01,976 - __main__ - INFO - [OK] Web application configured
2025-05-31 13:56:01,980 - __main__ - INFO - [SERVER] Money Circle running at http://localhost:8085
2025-05-31 13:56:01,980 - __main__ - INFO - [READY] Platform ready for Money Circle investment club members
2025-05-31 13:56:01,980 - __main__ - INFO - [DATA] Advanced market data manager started
2025-05-31 13:56:01,980 - __main__ - INFO - [WS] Market data WebSocket streamer subscribed to updates
2025-05-31 13:56:01,980 - __main__ - INFO - [NOTIFY] Notification system started
2025-05-31 13:56:01,980 - __main__ - INFO - [TRADING] Live trading monitoring started
2025-05-31 13:56:01,982 - __main__ - INFO - [STRATEGY] Strategy automation started
2025-05-31 13:56:01,982 - market_data.advanced_market_data_manager - INFO - [DATA] Starting advanced market data manager...
2025-05-31 13:56:01,982 - notifications.notification_manager - INFO - [NOTIFY] Starting notification delivery system...
2025-05-31 13:56:01,982 - trading.live_trading_interface - INFO - [TRADING] Starting real-time trading monitoring...
2025-05-31 13:56:01,982 - strategies.live_strategy_engine - INFO - [STRATEGY] Starting live strategy automation...
2025-05-31 13:56:01,982 - market_data.advanced_market_data_manager - INFO - [DATA] Connecting to Binance WebSocket...
2025-05-31 13:56:01,999 - market_data.advanced_market_data_manager - INFO - [DATA] Connecting to HTX WebSocket...
2025-05-31 13:56:02,000 - market_data.advanced_market_data_manager - INFO - 🔄 Connecting to Bybit Spot WebSocket...
2025-05-31 13:56:02,007 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:56:02,007 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 0.0% (0 excellent, 0 good)
2025-05-31 13:56:03,505 - market_data.advanced_market_data_manager - INFO - [DATA] Connected to Binance WebSocket
2025-05-31 13:56:03,627 - market_data.advanced_market_data_manager - INFO - ✅ Connected to Bybit Spot WebSocket
2025-05-31 13:56:03,627 - market_data.advanced_market_data_manager - INFO - 📡 Subscribed to Bybit tickers: ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
2025-05-31 13:56:03,643 - market_data.advanced_market_data_manager - INFO - [DATA] Connected to HTX WebSocket
2025-05-31 13:56:03,917 - market_data.advanced_market_data_manager - INFO - ✅ Bybit subscription confirmed: subscribe
2025-05-31 13:56:31,088 - aiohttp.access - INFO - ::1 [31/May/2025:12:56:31 -0600] "GET /login HTTP/1.1" 200 17085 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:56:31,304 - aiohttp.access - INFO - ::1 [31/May/2025:12:56:31 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 172 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:56:38,277 - auth.user_manager - INFO - [AUTH] Successful login: epinnox from ::1
2025-05-31 13:56:38,278 - auth.user_manager - INFO - [AUTH] Session created for user: epinnox
2025-05-31 13:56:38,278 - __main__ - INFO - [OK] User logged in: epinnox
2025-05-31 13:56:38,278 - aiohttp.access - INFO - ::1 [31/May/2025:12:56:38 -0600] "POST /login HTTP/1.1" 302 408 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:56:38,282 - aiohttp.server - ERROR - Error handling request
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_protocol.py", line 477, in _handle_request
    resp = await request_handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_app.py", line 567, in _handle
    return await handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_middlewares.py", line 117, in impl
    return await handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_session\__init__.py", line 191, in factory
    response = await handler(request)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\app.py", line 206, in auth_middleware
    return await handler(request)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\dashboards\personal_dashboard.py", line 54, in serve_personal_dashboard
    'created_at': exchange.created_at.isoformat() if exchange.created_at else None
AttributeError: 'str' object has no attribute 'isoformat'
2025-05-31 13:56:38,284 - aiohttp.access - INFO - ::1 [31/May/2025:12:56:38 -0600] "GET /dashboard HTTP/1.1" 500 336 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:57:02,010 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:57:02,010 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
