#!/usr/bin/env python3
"""
Money Circle Market Data API
Professional REST API endpoints for real-time market data access.
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from aiohttp import web
from dataclasses import asdict

from .advanced_market_data_manager import AdvancedMarketDataManager, MarketTick, DataSource, DataQuality

logger = logging.getLogger(__name__)

class MarketDataAPI:
    """Professional market data API with comprehensive endpoints."""
    
    def __init__(self, market_data_manager: AdvancedMarketDataManager):
        self.market_data_manager = market_data_manager
        self.request_count = 0
        self.start_time = datetime.now()
        
        logger.info("📡 Market Data API initialized")

    def setup_routes(self, app: web.Application):
        """Setup API routes."""
        # Market data endpoints
        app.router.add_get('/api/market/ticker/{symbol}', self.get_ticker)
        app.router.add_get('/api/market/tickers', self.get_all_tickers)
        app.router.add_get('/api/market/status', self.get_market_status)
        app.router.add_get('/api/market/health', self.get_health_status)
        
        # Real-time data endpoints
        app.router.add_get('/api/market/realtime/{symbol}', self.get_realtime_data)
        app.router.add_get('/api/market/quality', self.get_data_quality)
        
        # Historical data endpoints
        app.router.add_get('/api/market/history/{symbol}', self.get_price_history)
        app.router.add_get('/api/market/stats/{symbol}', self.get_market_stats)
        
        logger.info("✅ Market Data API routes configured")

    async def get_ticker(self, request: web.Request) -> web.Response:
        """Get ticker data for a specific symbol."""
        try:
            self.request_count += 1
            symbol = request.match_info['symbol'].upper()
            
            # Convert URL format to standard format
            if '/' not in symbol and symbol.endswith('USDT'):
                base = symbol[:-4]
                symbol = f"{base}/USDT"
            
            tick = self.market_data_manager.get_market_tick(symbol)
            
            if not tick:
                return web.json_response({
                    'error': f'Symbol {symbol} not found',
                    'available_symbols': list(self.market_data_manager.symbols)
                }, status=404)
            
            # Convert to JSON-serializable format
            tick_data = asdict(tick)
            tick_data['timestamp'] = tick.timestamp.isoformat()
            tick_data['source'] = tick.source.value
            tick_data['quality'] = tick.quality.value
            
            return web.json_response({
                'success': True,
                'symbol': symbol,
                'data': tick_data,
                'server_time': datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"❌ Error getting ticker for {symbol}: {e}")
            return web.json_response({
                'error': 'Failed to get ticker data',
                'details': str(e)
            }, status=500)

    async def get_all_tickers(self, request: web.Request) -> web.Response:
        """Get ticker data for all symbols."""
        try:
            self.request_count += 1
            all_ticks = self.market_data_manager.get_all_market_ticks()
            
            tickers = {}
            for symbol, tick in all_ticks.items():
                tick_data = asdict(tick)
                tick_data['timestamp'] = tick.timestamp.isoformat()
                tick_data['source'] = tick.source.value
                tick_data['quality'] = tick.quality.value
                tickers[symbol] = tick_data
            
            return web.json_response({
                'success': True,
                'count': len(tickers),
                'tickers': tickers,
                'server_time': datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"❌ Error getting all tickers: {e}")
            return web.json_response({
                'error': 'Failed to get ticker data',
                'details': str(e)
            }, status=500)

    async def get_market_status(self, request: web.Request) -> web.Response:
        """Get overall market status and connection health."""
        try:
            self.request_count += 1
            connection_status = self.market_data_manager.get_connection_status()
            all_ticks = self.market_data_manager.get_all_market_ticks()
            
            # Calculate status metrics
            total_connections = len(connection_status)
            active_connections = sum(1 for status in connection_status.values() if status)
            
            # Data quality breakdown
            quality_breakdown = {}
            for quality in DataQuality:
                quality_breakdown[quality.value] = sum(
                    1 for tick in all_ticks.values() if tick.quality == quality
                )
            
            # Calculate uptime
            uptime_seconds = (datetime.now() - self.start_time).total_seconds()
            
            return web.json_response({
                'success': True,
                'status': {
                    'overall_health': 'healthy' if active_connections > 0 else 'degraded',
                    'active_connections': active_connections,
                    'total_connections': total_connections,
                    'connection_rate': (active_connections / total_connections * 100) if total_connections > 0 else 0,
                    'data_sources': {source.value: status for source, status in connection_status.items()},
                    'symbols_tracked': len(all_ticks),
                    'data_quality': quality_breakdown,
                    'uptime_seconds': uptime_seconds,
                    'requests_served': self.request_count
                },
                'server_time': datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"❌ Error getting market status: {e}")
            return web.json_response({
                'error': 'Failed to get market status',
                'details': str(e)
            }, status=500)

    async def get_health_status(self, request: web.Request) -> web.Response:
        """Get detailed health status for monitoring."""
        try:
            self.request_count += 1
            connection_status = self.market_data_manager.get_connection_status()
            all_ticks = self.market_data_manager.get_all_market_ticks()
            
            # Check data freshness
            current_time = datetime.now()
            stale_data_count = 0
            fresh_data_count = 0
            
            for symbol, tick in all_ticks.items():
                age = (current_time - tick.timestamp).total_seconds()
                if age > 300:  # 5 minutes
                    stale_data_count += 1
                else:
                    fresh_data_count += 1
            
            # Determine overall health
            active_connections = sum(1 for status in connection_status.values() if status)
            health_score = (
                (active_connections / len(connection_status) * 50) +
                (fresh_data_count / len(all_ticks) * 50) if len(all_ticks) > 0 else 0
            )
            
            health_status = 'healthy' if health_score >= 80 else 'degraded' if health_score >= 50 else 'unhealthy'
            
            return web.json_response({
                'success': True,
                'health': {
                    'status': health_status,
                    'score': round(health_score, 1),
                    'connections': {
                        'active': active_connections,
                        'total': len(connection_status),
                        'details': {source.value: status for source, status in connection_status.items()}
                    },
                    'data': {
                        'fresh': fresh_data_count,
                        'stale': stale_data_count,
                        'total': len(all_ticks)
                    },
                    'uptime': (datetime.now() - self.start_time).total_seconds(),
                    'requests': self.request_count
                },
                'timestamp': datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"❌ Error getting health status: {e}")
            return web.json_response({
                'error': 'Failed to get health status',
                'details': str(e)
            }, status=500)

    async def get_realtime_data(self, request: web.Request) -> web.Response:
        """Get real-time data with enhanced metadata."""
        try:
            self.request_count += 1
            symbol = request.match_info['symbol'].upper()
            
            # Convert URL format to standard format
            if '/' not in symbol and symbol.endswith('USDT'):
                base = symbol[:-4]
                symbol = f"{base}/USDT"
            
            tick = self.market_data_manager.get_market_tick(symbol)
            
            if not tick:
                return web.json_response({
                    'error': f'Symbol {symbol} not found'
                }, status=404)
            
            # Calculate additional metrics
            spread = tick.ask - tick.bid
            spread_percent = (spread / tick.price * 100) if tick.price > 0 else 0
            
            # Data age
            data_age_seconds = (datetime.now() - tick.timestamp).total_seconds()
            
            return web.json_response({
                'success': True,
                'symbol': symbol,
                'price': tick.price,
                'bid': tick.bid,
                'ask': tick.ask,
                'spread': round(spread, 8),
                'spread_percent': round(spread_percent, 4),
                'volume_24h': tick.volume_24h,
                'change_24h': tick.change_24h,
                'change_24h_percent': round(tick.change_24h_percent, 2),
                'high_24h': tick.high_24h,
                'low_24h': tick.low_24h,
                'metadata': {
                    'source': tick.source.value,
                    'quality': tick.quality.value,
                    'latency_ms': tick.latency_ms,
                    'data_age_seconds': round(data_age_seconds, 1),
                    'timestamp': tick.timestamp.isoformat()
                },
                'server_time': datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"❌ Error getting realtime data for {symbol}: {e}")
            return web.json_response({
                'error': 'Failed to get realtime data',
                'details': str(e)
            }, status=500)

    async def get_data_quality(self, request: web.Request) -> web.Response:
        """Get comprehensive data quality metrics."""
        try:
            self.request_count += 1
            all_ticks = self.market_data_manager.get_all_market_ticks()
            connection_status = self.market_data_manager.get_connection_status()
            
            # Quality metrics by symbol
            symbol_quality = {}
            for symbol, tick in all_ticks.items():
                age = (datetime.now() - tick.timestamp).total_seconds()
                symbol_quality[symbol] = {
                    'quality': tick.quality.value,
                    'source': tick.source.value,
                    'age_seconds': round(age, 1),
                    'latency_ms': tick.latency_ms,
                    'is_fresh': age < 60  # Fresh if less than 1 minute old
                }
            
            # Overall quality score
            quality_scores = {
                DataQuality.EXCELLENT: 100,
                DataQuality.GOOD: 75,
                DataQuality.FAIR: 50,
                DataQuality.POOR: 25
            }
            
            total_score = sum(quality_scores.get(tick.quality, 0) for tick in all_ticks.values())
            average_quality = total_score / len(all_ticks) if all_ticks else 0
            
            # Source reliability
            source_stats = {}
            for source in DataSource:
                source_ticks = [tick for tick in all_ticks.values() if tick.source == source]
                if source_ticks:
                    avg_latency = sum(tick.latency_ms for tick in source_ticks) / len(source_ticks)
                    source_stats[source.value] = {
                        'symbols_count': len(source_ticks),
                        'average_latency_ms': round(avg_latency, 1),
                        'connected': connection_status.get(source, False)
                    }
            
            return web.json_response({
                'success': True,
                'quality': {
                    'overall_score': round(average_quality, 1),
                    'symbols': symbol_quality,
                    'sources': source_stats,
                    'summary': {
                        'total_symbols': len(all_ticks),
                        'excellent_quality': sum(1 for tick in all_ticks.values() if tick.quality == DataQuality.EXCELLENT),
                        'good_quality': sum(1 for tick in all_ticks.values() if tick.quality == DataQuality.GOOD),
                        'fair_quality': sum(1 for tick in all_ticks.values() if tick.quality == DataQuality.FAIR),
                        'poor_quality': sum(1 for tick in all_ticks.values() if tick.quality == DataQuality.POOR)
                    }
                },
                'timestamp': datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"❌ Error getting data quality: {e}")
            return web.json_response({
                'error': 'Failed to get data quality',
                'details': str(e)
            }, status=500)

    async def get_price_history(self, request: web.Request) -> web.Response:
        """Get price history for a symbol (mock implementation)."""
        try:
            self.request_count += 1
            symbol = request.match_info['symbol'].upper()
            
            # Convert URL format to standard format
            if '/' not in symbol and symbol.endswith('USDT'):
                base = symbol[:-4]
                symbol = f"{base}/USDT"
            
            # Get current tick
            current_tick = self.market_data_manager.get_market_tick(symbol)
            if not current_tick:
                return web.json_response({
                    'error': f'Symbol {symbol} not found'
                }, status=404)
            
            # Generate mock historical data (in production, this would come from a database)
            import random
            history = []
            current_price = current_tick.price
            
            for i in range(24):  # 24 hours of hourly data
                timestamp = datetime.now() - timedelta(hours=23-i)
                # Add some realistic price movement
                price_change = random.uniform(-0.02, 0.02)  # ±2%
                price = current_price * (1 + price_change * (23-i) / 23)
                
                history.append({
                    'timestamp': timestamp.isoformat(),
                    'price': round(price, 2),
                    'volume': random.uniform(100000, 1000000)
                })
            
            return web.json_response({
                'success': True,
                'symbol': symbol,
                'interval': '1h',
                'data': history,
                'count': len(history)
            })
            
        except Exception as e:
            logger.error(f"❌ Error getting price history for {symbol}: {e}")
            return web.json_response({
                'error': 'Failed to get price history',
                'details': str(e)
            }, status=500)

    async def get_market_stats(self, request: web.Request) -> web.Response:
        """Get comprehensive market statistics for a symbol."""
        try:
            self.request_count += 1
            symbol = request.match_info['symbol'].upper()
            
            # Convert URL format to standard format
            if '/' not in symbol and symbol.endswith('USDT'):
                base = symbol[:-4]
                symbol = f"{base}/USDT"
            
            tick = self.market_data_manager.get_market_tick(symbol)
            if not tick:
                return web.json_response({
                    'error': f'Symbol {symbol} not found'
                }, status=404)
            
            # Calculate additional statistics
            spread = tick.ask - tick.bid
            spread_percent = (spread / tick.price * 100) if tick.price > 0 else 0
            
            # Price range
            price_range = tick.high_24h - tick.low_24h
            price_range_percent = (price_range / tick.low_24h * 100) if tick.low_24h > 0 else 0
            
            # Current position in range
            if price_range > 0:
                position_in_range = (tick.price - tick.low_24h) / price_range * 100
            else:
                position_in_range = 50  # Middle if no range
            
            return web.json_response({
                'success': True,
                'symbol': symbol,
                'statistics': {
                    'current_price': tick.price,
                    'bid_ask_spread': {
                        'absolute': round(spread, 8),
                        'percent': round(spread_percent, 4)
                    },
                    'daily_range': {
                        'high': tick.high_24h,
                        'low': tick.low_24h,
                        'range': round(price_range, 2),
                        'range_percent': round(price_range_percent, 2),
                        'current_position_percent': round(position_in_range, 1)
                    },
                    'volume': {
                        'volume_24h': tick.volume_24h,
                        'volume_usd_24h': tick.volume_24h * tick.price
                    },
                    'change': {
                        'absolute': tick.change_24h,
                        'percent': round(tick.change_24h_percent, 2)
                    },
                    'data_quality': {
                        'source': tick.source.value,
                        'quality': tick.quality.value,
                        'latency_ms': tick.latency_ms,
                        'last_update': tick.timestamp.isoformat()
                    }
                },
                'timestamp': datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"❌ Error getting market stats for {symbol}: {e}")
            return web.json_response({
                'error': 'Failed to get market statistics',
                'details': str(e)
            }, status=500)
