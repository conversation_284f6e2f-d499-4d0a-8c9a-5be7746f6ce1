#!/usr/bin/env python3
"""
Money Circle Live Strategy Automation Engine
Advanced strategy execution with real-time signal processing, risk management, and performance tracking.
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import numpy as np

logger = logging.getLogger(__name__)

class StrategyStatus(Enum):
    INACTIVE = "inactive"
    ACTIVE = "active"
    PAUSED = "paused"
    ERROR = "error"
    STOPPED = "stopped"

class SignalStrength(Enum):
    WEAK = "weak"
    MODERATE = "moderate"
    STRONG = "strong"
    VERY_STRONG = "very_strong"

@dataclass
class TradingSignal:
    """Enhanced trading signal with confidence and metadata."""
    strategy_id: str
    symbol: str
    action: str  # BUY, SELL, HOLD
    strength: SignalStrength
    confidence: float  # 0.0 to 1.0
    price_target: Optional[float]
    stop_loss: Optional[float]
    take_profit: Optional[float]
    position_size: float
    rationale: str
    timestamp: datetime
    metadata: Dict[str, Any]

@dataclass
class StrategyPerformance:
    """Real-time strategy performance metrics."""
    strategy_id: str
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    total_pnl: float
    total_pnl_percent: float
    max_drawdown: float
    sharpe_ratio: float
    avg_trade_duration: timedelta
    last_signal_time: datetime
    signals_today: int
    status: StrategyStatus

class LiveStrategyEngine:
    """Advanced live strategy automation engine."""
    
    def __init__(self, db_manager, trading_interface, market_data_manager):
        self.db = db_manager
        self.trading_interface = trading_interface
        self.market_data = market_data_manager
        
        # Strategy management
        self.active_strategies: Dict[str, Dict[str, Any]] = {}
        self.strategy_performances: Dict[str, StrategyPerformance] = {}
        self.signal_processors: Dict[str, Callable] = {}
        
        # Real-time execution
        self.execution_active = False
        self.signal_queue: asyncio.Queue = asyncio.Queue()
        self.execution_interval = 1.0  # 1 second
        
        # Risk management
        self.max_concurrent_trades = 5
        self.max_daily_signals = 50
        self.min_signal_confidence = 0.6
        
        # Performance tracking
        self.performance_update_interval = 60  # 1 minute
        
        logger.info("⚡ Live Strategy Engine initialized")

    async def start_strategy_automation(self):
        """Start automated strategy execution."""
        if self.execution_active:
            return
        
        self.execution_active = True
        logger.info("🚀 Starting live strategy automation...")
        
        # Start automation tasks
        tasks = [
            self._signal_generation_loop(),
            self._signal_execution_loop(),
            self._performance_monitoring_loop(),
            self._risk_monitoring_loop()
        ]
        
        await asyncio.gather(*tasks, return_exceptions=True)

    async def register_strategy(self, strategy_config: Dict[str, Any]) -> bool:
        """Register a new strategy for live execution."""
        try:
            strategy_id = strategy_config['id']
            
            # Validate strategy configuration
            if not self._validate_strategy_config(strategy_config):
                logger.error(f"❌ Invalid strategy configuration: {strategy_id}")
                return False
            
            # Initialize strategy
            strategy = {
                'id': strategy_id,
                'name': strategy_config['name'],
                'type': strategy_config['type'],
                'symbols': strategy_config['symbols'],
                'timeframes': strategy_config['timeframes'],
                'parameters': strategy_config.get('parameters', {}),
                'risk_settings': strategy_config.get('risk_settings', {}),
                'user_assignments': strategy_config.get('user_assignments', []),
                'status': StrategyStatus.INACTIVE,
                'created_at': datetime.now(),
                'last_signal': None
            }
            
            self.active_strategies[strategy_id] = strategy
            
            # Initialize performance tracking
            self.strategy_performances[strategy_id] = StrategyPerformance(
                strategy_id=strategy_id,
                total_trades=0,
                winning_trades=0,
                losing_trades=0,
                win_rate=0.0,
                total_pnl=0.0,
                total_pnl_percent=0.0,
                max_drawdown=0.0,
                sharpe_ratio=0.0,
                avg_trade_duration=timedelta(hours=1),
                last_signal_time=datetime.now(),
                signals_today=0,
                status=StrategyStatus.INACTIVE
            )
            
            # Register signal processor
            processor_name = f"process_{strategy_config['type']}_signals"
            if hasattr(self, processor_name):
                self.signal_processors[strategy_id] = getattr(self, processor_name)
            else:
                self.signal_processors[strategy_id] = self._default_signal_processor
            
            logger.info(f"✅ Strategy registered: {strategy_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error registering strategy: {e}")
            return False

    async def activate_strategy(self, strategy_id: str, user_id: int) -> bool:
        """Activate a strategy for live execution."""
        try:
            if strategy_id not in self.active_strategies:
                logger.error(f"❌ Strategy not found: {strategy_id}")
                return False
            
            strategy = self.active_strategies[strategy_id]
            
            # Check user permissions
            if not self._check_strategy_permissions(strategy_id, user_id):
                logger.error(f"❌ User {user_id} not authorized for strategy {strategy_id}")
                return False
            
            # Activate strategy
            strategy['status'] = StrategyStatus.ACTIVE
            strategy['activated_by'] = user_id
            strategy['activated_at'] = datetime.now()
            
            # Update performance status
            self.strategy_performances[strategy_id].status = StrategyStatus.ACTIVE
            
            logger.info(f"🟢 Strategy activated: {strategy_id} by user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error activating strategy: {e}")
            return False

    async def _signal_generation_loop(self):
        """Main signal generation loop for all active strategies."""
        while self.execution_active:
            try:
                for strategy_id, strategy in self.active_strategies.items():
                    if strategy['status'] != StrategyStatus.ACTIVE:
                        continue
                    
                    # Generate signals for this strategy
                    signals = await self._generate_strategy_signals(strategy_id)
                    
                    # Queue signals for execution
                    for signal in signals:
                        await self.signal_queue.put(signal)
                
                await asyncio.sleep(self.execution_interval)
                
            except Exception as e:
                logger.error(f"❌ Error in signal generation loop: {e}")
                await asyncio.sleep(5)

    async def _signal_execution_loop(self):
        """Execute signals from the queue with risk management."""
        while self.execution_active:
            try:
                # Get signal from queue (with timeout)
                try:
                    signal = await asyncio.wait_for(self.signal_queue.get(), timeout=1.0)
                except asyncio.TimeoutError:
                    continue
                
                # Execute signal with risk checks
                await self._execute_signal_with_risk_management(signal)
                
            except Exception as e:
                logger.error(f"❌ Error in signal execution loop: {e}")
                await asyncio.sleep(1)

    async def _generate_strategy_signals(self, strategy_id: str) -> List[TradingSignal]:
        """Generate signals for a specific strategy."""
        try:
            strategy = self.active_strategies[strategy_id]
            signals = []
            
            # Get signal processor for this strategy
            processor = self.signal_processors.get(strategy_id, self._default_signal_processor)
            
            # Generate signals for each symbol
            for symbol in strategy['symbols']:
                # Get latest market data
                market_data = await self.market_data.get_latest_data(symbol)
                if not market_data:
                    continue
                
                # Process signals
                strategy_signals = await processor(strategy, symbol, market_data)
                signals.extend(strategy_signals)
            
            # Update last signal time
            if signals:
                strategy['last_signal'] = datetime.now()
                self.strategy_performances[strategy_id].last_signal_time = datetime.now()
                self.strategy_performances[strategy_id].signals_today += len(signals)
            
            return signals
            
        except Exception as e:
            logger.error(f"❌ Error generating signals for {strategy_id}: {e}")
            return []

    async def process_vwap_signals(self, strategy: Dict[str, Any], symbol: str, market_data: Dict[str, Any]) -> List[TradingSignal]:
        """Process VWAP strategy signals."""
        try:
            signals = []
            
            # Get VWAP parameters
            params = strategy['parameters']
            lookback_period = params.get('lookback_period', 20)
            deviation_threshold = params.get('deviation_threshold', 0.02)
            
            # Calculate VWAP
            prices = np.array([float(d['close']) for d in market_data.get('ohlcv', [])])
            volumes = np.array([float(d['volume']) for d in market_data.get('ohlcv', [])])
            
            if len(prices) < lookback_period:
                return signals
            
            # VWAP calculation
            vwap = np.sum(prices[-lookback_period:] * volumes[-lookback_period:]) / np.sum(volumes[-lookback_period:])
            current_price = prices[-1]
            
            # Calculate deviation
            deviation = (current_price - vwap) / vwap
            
            # Generate signal based on deviation
            if abs(deviation) > deviation_threshold:
                action = "SELL" if deviation > 0 else "BUY"
                strength = SignalStrength.STRONG if abs(deviation) > deviation_threshold * 2 else SignalStrength.MODERATE
                confidence = min(0.95, abs(deviation) / deviation_threshold * 0.5 + 0.5)
                
                signal = TradingSignal(
                    strategy_id=strategy['id'],
                    symbol=symbol,
                    action=action,
                    strength=strength,
                    confidence=confidence,
                    price_target=vwap,
                    stop_loss=current_price * (0.98 if action == "BUY" else 1.02),
                    take_profit=current_price * (1.02 if action == "BUY" else 0.98),
                    position_size=self._calculate_position_size(strategy, confidence),
                    rationale=f"VWAP deviation: {deviation:.3f}, threshold: {deviation_threshold}",
                    timestamp=datetime.now(),
                    metadata={'vwap': vwap, 'deviation': deviation}
                )
                
                signals.append(signal)
            
            return signals
            
        except Exception as e:
            logger.error(f"❌ Error processing VWAP signals: {e}")
            return []

    async def process_momentum_signals(self, strategy: Dict[str, Any], symbol: str, market_data: Dict[str, Any]) -> List[TradingSignal]:
        """Process momentum strategy signals."""
        try:
            signals = []
            
            # Get momentum parameters
            params = strategy['parameters']
            rsi_period = params.get('rsi_period', 14)
            rsi_oversold = params.get('rsi_oversold', 30)
            rsi_overbought = params.get('rsi_overbought', 70)
            
            # Calculate RSI
            prices = np.array([float(d['close']) for d in market_data.get('ohlcv', [])])
            
            if len(prices) < rsi_period + 1:
                return signals
            
            # Simple RSI calculation
            deltas = np.diff(prices)
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)
            
            avg_gain = np.mean(gains[-rsi_period:])
            avg_loss = np.mean(losses[-rsi_period:])
            
            if avg_loss == 0:
                rsi = 100
            else:
                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))
            
            # Generate signals based on RSI
            if rsi < rsi_oversold:
                action = "BUY"
                strength = SignalStrength.STRONG if rsi < 20 else SignalStrength.MODERATE
                confidence = (rsi_oversold - rsi) / rsi_oversold * 0.4 + 0.6
            elif rsi > rsi_overbought:
                action = "SELL"
                strength = SignalStrength.STRONG if rsi > 80 else SignalStrength.MODERATE
                confidence = (rsi - rsi_overbought) / (100 - rsi_overbought) * 0.4 + 0.6
            else:
                return signals
            
            signal = TradingSignal(
                strategy_id=strategy['id'],
                symbol=symbol,
                action=action,
                strength=strength,
                confidence=confidence,
                price_target=None,
                stop_loss=prices[-1] * (0.97 if action == "BUY" else 1.03),
                take_profit=prices[-1] * (1.03 if action == "BUY" else 0.97),
                position_size=self._calculate_position_size(strategy, confidence),
                rationale=f"RSI momentum: {rsi:.1f}, threshold: {rsi_oversold if action == 'BUY' else rsi_overbought}",
                timestamp=datetime.now(),
                metadata={'rsi': rsi, 'avg_gain': avg_gain, 'avg_loss': avg_loss}
            )
            
            signals.append(signal)
            return signals
            
        except Exception as e:
            logger.error(f"❌ Error processing momentum signals: {e}")
            return []

    async def _default_signal_processor(self, strategy: Dict[str, Any], symbol: str, market_data: Dict[str, Any]) -> List[TradingSignal]:
        """Default signal processor for unknown strategy types."""
        logger.warning(f"⚠️ Using default signal processor for strategy {strategy['id']}")
        return []

    async def _execute_signal_with_risk_management(self, signal: TradingSignal):
        """Execute signal with comprehensive risk management."""
        try:
            # Check signal confidence threshold
            if signal.confidence < self.min_signal_confidence:
                logger.debug(f"🔍 Signal confidence too low: {signal.confidence:.2f}")
                return
            
            # Check daily signal limits
            strategy_perf = self.strategy_performances[signal.strategy_id]
            if strategy_perf.signals_today >= self.max_daily_signals:
                logger.warning(f"⚠️ Daily signal limit reached for {signal.strategy_id}")
                return
            
            # Get strategy user assignments
            strategy = self.active_strategies[signal.strategy_id]
            user_assignments = strategy.get('user_assignments', [])
            
            # Execute for each assigned user
            for user_assignment in user_assignments:
                user_id = user_assignment['user_id']
                allocation = user_assignment.get('allocation', 1.0)
                
                # Adjust position size based on user allocation
                adjusted_size = signal.position_size * allocation
                
                # Prepare order parameters
                order_params = {
                    'exchange': user_assignment.get('exchange', 'binance'),
                    'symbol': signal.symbol,
                    'side': signal.action.lower(),
                    'amount': adjusted_size,
                    'type': 'market',
                    'strategy': signal.strategy_id,
                    'metadata': {
                        'signal_confidence': signal.confidence,
                        'signal_strength': signal.strength.value,
                        'rationale': signal.rationale,
                        'stop_loss': signal.stop_loss,
                        'take_profit': signal.take_profit
                    }
                }
                
                # Execute order through trading interface
                order = await self.trading_interface.place_advanced_order(user_id, order_params)
                
                if order:
                    logger.info(f"✅ Signal executed: {signal.strategy_id} -> {signal.action} {signal.symbol} for user {user_id}")
                    
                    # Update strategy performance
                    await self._update_strategy_performance(signal.strategy_id, order)
                else:
                    logger.warning(f"❌ Failed to execute signal for user {user_id}")
            
        except Exception as e:
            logger.error(f"❌ Error executing signal: {e}")

    def _calculate_position_size(self, strategy: Dict[str, Any], confidence: float) -> float:
        """Calculate position size based on strategy settings and signal confidence."""
        try:
            risk_settings = strategy.get('risk_settings', {})
            base_position_size = risk_settings.get('base_position_size', 0.01)  # 1% of portfolio
            max_position_size = risk_settings.get('max_position_size', 0.05)   # 5% of portfolio
            
            # Adjust size based on confidence
            confidence_multiplier = confidence * 2  # 0.6 confidence = 1.2x, 1.0 confidence = 2.0x
            adjusted_size = base_position_size * confidence_multiplier
            
            return min(adjusted_size, max_position_size)
            
        except Exception as e:
            logger.error(f"❌ Error calculating position size: {e}")
            return 0.01  # Conservative fallback

    async def _performance_monitoring_loop(self):
        """Monitor and update strategy performance metrics."""
        while self.execution_active:
            try:
                for strategy_id in self.active_strategies.keys():
                    await self._update_strategy_performance_metrics(strategy_id)
                
                await asyncio.sleep(self.performance_update_interval)
                
            except Exception as e:
                logger.error(f"❌ Error in performance monitoring: {e}")
                await asyncio.sleep(30)

    async def _risk_monitoring_loop(self):
        """Monitor strategy risk levels and trigger alerts."""
        while self.execution_active:
            try:
                for strategy_id, performance in self.strategy_performances.items():
                    # Check for excessive drawdown
                    if performance.max_drawdown > 0.10:  # 10% max drawdown
                        await self._pause_strategy_for_risk(strategy_id, "Excessive drawdown")
                    
                    # Check for poor performance
                    if performance.total_trades > 10 and performance.win_rate < 0.3:  # 30% win rate
                        await self._pause_strategy_for_risk(strategy_id, "Poor win rate")
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"❌ Error in risk monitoring: {e}")
                await asyncio.sleep(60)

    def _validate_strategy_config(self, config: Dict[str, Any]) -> bool:
        """Validate strategy configuration."""
        required_fields = ['id', 'name', 'type', 'symbols']
        return all(field in config for field in required_fields)

    def _check_strategy_permissions(self, strategy_id: str, user_id: int) -> bool:
        """Check if user has permission to control strategy."""
        # This would integrate with the club governance system
        return True  # Simplified for now

    async def _update_strategy_performance(self, strategy_id: str, order):
        """Update strategy performance after order execution."""
        try:
            performance = self.strategy_performances[strategy_id]
            performance.total_trades += 1
            
            # This would be updated when orders are filled with actual P&L
            # For now, we'll update basic metrics
            
        except Exception as e:
            logger.error(f"❌ Error updating strategy performance: {e}")

    async def _update_strategy_performance_metrics(self, strategy_id: str):
        """Update comprehensive performance metrics for strategy."""
        try:
            # This would calculate detailed performance metrics from trade history
            pass
            
        except Exception as e:
            logger.error(f"❌ Error updating performance metrics: {e}")

    async def _pause_strategy_for_risk(self, strategy_id: str, reason: str):
        """Pause strategy due to risk concerns."""
        try:
            if strategy_id in self.active_strategies:
                self.active_strategies[strategy_id]['status'] = StrategyStatus.PAUSED
                self.strategy_performances[strategy_id].status = StrategyStatus.PAUSED
                
                logger.warning(f"⏸️ Strategy paused for risk: {strategy_id} - {reason}")
                
                # This would send notifications to users
                
        except Exception as e:
            logger.error(f"❌ Error pausing strategy: {e}")

    def get_strategy_performance(self, strategy_id: str) -> Optional[StrategyPerformance]:
        """Get performance metrics for strategy."""
        return self.strategy_performances.get(strategy_id)

    def get_active_strategies(self) -> Dict[str, Dict[str, Any]]:
        """Get all active strategies."""
        return {k: v for k, v in self.active_strategies.items() if v['status'] == StrategyStatus.ACTIVE}

    async def stop_automation(self):
        """Stop strategy automation."""
        self.execution_active = False
        logger.info("🛑 Strategy automation stopped")
