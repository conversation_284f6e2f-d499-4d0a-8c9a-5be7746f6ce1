#!/usr/bin/env python3
"""
Money Circle Database Models
Database schema and models for the investment club platform.
"""

import sqlite3
import logging
from datetime import datetime
from pathlib import Path
from typing import Optional, List, Dict, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class User:
    """User model."""
    id: Optional[int] = None
    username: str = ""
    email: str = ""
    hashed_password: str = ""
    role: str = "member"  # admin, member, viewer
    date_joined: Optional[datetime] = None
    last_login: Optional[datetime] = None
    email_verified: bool = False
    agreement_accepted: bool = False
    is_active: bool = True

@dataclass
class MembershipAgreement:
    """Membership agreement acceptance model."""
    id: Optional[int] = None
    user_id: int = 0
    agreement_version: str = "v1.0"
    agreement_text: str = ""
    ip_address: str = ""
    user_agent: str = ""
    agreed_at: Optional[datetime] = None

@dataclass
class UserExchange:
    """User exchange account model."""
    id: Optional[int] = None
    user_id: int = 0
    exchange_name: str = ""
    api_key_encrypted: bytes = b""
    secret_key_encrypted: bytes = b""
    passphrase_encrypted: Optional[bytes] = None
    is_active: bool = True
    created_at: Optional[datetime] = None

@dataclass
class StrategyProposal:
    """Strategy proposal model."""
    id: Optional[int] = None
    name: str = ""
    description: str = ""
    proposed_by: int = 0
    status: str = "pending"  # pending, approved, rejected
    created_at: Optional[datetime] = None

@dataclass
class StrategyVote:
    """Strategy vote model."""
    id: Optional[int] = None
    strategy_id: int = 0
    user_id: int = 0
    vote: str = ""  # up, down
    comment: str = ""
    timestamp: Optional[datetime] = None

@dataclass
class UserPosition:
    """User trading position model."""
    id: Optional[int] = None
    user_id: int = 0
    exchange_name: str = ""
    symbol: str = ""
    side: str = ""  # long, short
    size: float = 0.0
    entry_price: float = 0.0
    current_price: float = 0.0
    pnl: float = 0.0
    leverage: float = 1.0
    status: str = "open"  # open, closed
    created_at: Optional[datetime] = None
    closed_at: Optional[datetime] = None

@dataclass
class UserTrade:
    """User trade history model."""
    id: Optional[int] = None
    user_id: int = 0
    exchange_name: str = ""
    symbol: str = ""
    side: str = ""  # buy, sell
    size: float = 0.0
    price: float = 0.0
    fee: float = 0.0
    order_type: str = ""  # market, limit, stop
    strategy_name: str = ""
    timestamp: Optional[datetime] = None

class DatabaseManager:
    """Database manager for Money Circle platform."""

    def __init__(self, db_path: str):
        self.db_path = db_path
        self.conn = None
        self.init_database()

    def connect(self):
        """Connect to the database."""
        try:
            Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
            self.conn = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=10.0
            )
            self.conn.row_factory = sqlite3.Row
            return True
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            return False

    def init_database(self):
        """Initialize database with required tables."""
        if not self.connect():
            raise Exception("Failed to connect to database")

        try:
            # Create users table with new onboarding fields and case-insensitive unique constraints
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL COLLATE NOCASE,
                    email TEXT UNIQUE NOT NULL COLLATE NOCASE,
                    hashed_password TEXT NOT NULL,
                    role TEXT CHECK(role IN ('admin', 'member', 'viewer')) DEFAULT 'member',
                    date_joined TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP,
                    email_verified BOOLEAN DEFAULT FALSE,
                    agreement_accepted BOOLEAN DEFAULT FALSE,
                    is_active BOOLEAN DEFAULT TRUE
                )
            """)

            # Create membership agreements table
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS membership_agreements (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    agreement_version TEXT NOT NULL DEFAULT 'v1.0',
                    agreement_text TEXT NOT NULL,
                    ip_address TEXT,
                    user_agent TEXT,
                    agreed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
                )
            """)

            # Create user_exchanges table
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS user_exchanges (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    exchange_name TEXT NOT NULL,
                    api_key_encrypted BLOB NOT NULL,
                    secret_key_encrypted BLOB NOT NULL,
                    passphrase_encrypted BLOB,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
                )
            """)

            # Create strategy_proposals table (enhanced for club features)
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS strategy_proposals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    name TEXT NOT NULL,
                    description TEXT,
                    user_id INTEGER NOT NULL,
                    proposed_by INTEGER NOT NULL,
                    strategy_type TEXT NOT NULL,
                    risk_level TEXT CHECK(risk_level IN ('low', 'medium', 'high')) DEFAULT 'medium',
                    expected_return REAL DEFAULT 0.0,
                    max_drawdown REAL DEFAULT 0.0,
                    time_horizon TEXT DEFAULT 'medium_term',
                    parameters TEXT,
                    status TEXT CHECK(status IN ('pending_review', 'voting', 'approved', 'rejected')) DEFAULT 'pending_review',
                    admin_review_notes TEXT,
                    voting_deadline TIMESTAMP,
                    vote_result TEXT,
                    is_active BOOLEAN DEFAULT FALSE,
                    activated_at TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
                    FOREIGN KEY (proposed_by) REFERENCES users (id) ON DELETE CASCADE
                )
            """)

            # Create strategy_votes table (enhanced for club features)
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS strategy_votes (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    strategy_id INTEGER NOT NULL,
                    user_id INTEGER NOT NULL,
                    vote TEXT CHECK(vote IN ('approve', 'reject', 'abstain')) NOT NULL,
                    reasoning TEXT,
                    comment TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (strategy_id) REFERENCES strategy_proposals (id) ON DELETE CASCADE,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
                    UNIQUE(strategy_id, user_id)
                )
            """)

            # Create user_positions table
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS user_positions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    exchange_name TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    side TEXT CHECK(side IN ('long', 'short')) NOT NULL,
                    size REAL NOT NULL,
                    entry_price REAL NOT NULL,
                    current_price REAL DEFAULT 0.0,
                    pnl REAL DEFAULT 0.0,
                    leverage REAL DEFAULT 1.0,
                    status TEXT CHECK(status IN ('open', 'closed')) DEFAULT 'open',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    closed_at TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
                )
            """)

            # Create user_trades table
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS user_trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    exchange_name TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    side TEXT CHECK(side IN ('buy', 'sell')) NOT NULL,
                    size REAL NOT NULL,
                    price REAL NOT NULL,
                    fee REAL DEFAULT 0.0,
                    order_type TEXT NOT NULL,
                    strategy_name TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
                )
            """)

            # Create indexes for better performance
            self.conn.execute("CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)")
            self.conn.execute("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)")
            self.conn.execute("CREATE INDEX IF NOT EXISTS idx_user_exchanges_user_id ON user_exchanges(user_id)")
            self.conn.execute("CREATE INDEX IF NOT EXISTS idx_strategy_votes_strategy_id ON strategy_votes(strategy_id)")
            self.conn.execute("CREATE INDEX IF NOT EXISTS idx_user_positions_user_id ON user_positions(user_id)")
            self.conn.execute("CREATE INDEX IF NOT EXISTS idx_user_trades_user_id ON user_trades(user_id)")

            # Run database migrations for existing installations
            self._run_migrations()

            self.conn.commit()
            logger.info("[OK] Database initialized successfully")

        except Exception as e:
            logger.error(f"Database initialization error: {e}")
            raise

    def _run_migrations(self):
        """Run database migrations for existing installations."""
        try:
            # Check if we need to add new columns to existing users table
            cursor = self.conn.execute("PRAGMA table_info(users)")
            columns = [row[1] for row in cursor.fetchall()]

            # Add email_verified column if it doesn't exist
            if 'email_verified' not in columns:
                logger.info("[MIGRATION] Adding email_verified column to users table")
                self.conn.execute("ALTER TABLE users ADD COLUMN email_verified BOOLEAN DEFAULT FALSE")

            # Add agreement_accepted column if it doesn't exist
            if 'agreement_accepted' not in columns:
                logger.info("[MIGRATION] Adding agreement_accepted column to users table")
                self.conn.execute("ALTER TABLE users ADD COLUMN agreement_accepted BOOLEAN DEFAULT FALSE")

            # Add last_login column if it doesn't exist
            if 'last_login' not in columns:
                logger.info("[MIGRATION] Adding last_login column to users table")
                self.conn.execute("ALTER TABLE users ADD COLUMN last_login TIMESTAMP")

            # For existing admin users, mark agreement as accepted
            self.conn.execute("""
                UPDATE users
                SET agreement_accepted = TRUE, email_verified = TRUE
                WHERE role = 'admin' AND agreement_accepted = FALSE
            """)

            logger.info("[OK] Database migrations completed successfully")

        except Exception as e:
            logger.error(f"Migration error: {e}")
            # Don't raise - migrations are optional for compatibility

    def close(self):
        """Close database connection."""
        if self.conn:
            self.conn.close()
            self.conn = None
