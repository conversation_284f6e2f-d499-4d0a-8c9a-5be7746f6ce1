#!/usr/bin/env python3
"""
Money Circle Real-Time WebSocket Streamer
Professional WebSocket streaming for real-time market data delivery to clients.
"""

import asyncio
import json
import logging
import weakref
from datetime import datetime
from typing import Dict, Set, Any, Optional, List
from aiohttp import web, WSMsgType
from dataclasses import asdict

from .advanced_market_data_manager import AdvancedMarketDataManager, MarketTick

logger = logging.getLogger(__name__)

class WebSocketConnection:
    """Represents a WebSocket connection with subscription management."""
    
    def __init__(self, websocket: web.WebSocketResponse, user_id: Optional[int] = None):
        self.websocket = websocket
        self.user_id = user_id
        self.subscriptions: Set[str] = set()
        self.connected_at = datetime.now()
        self.last_ping = datetime.now()
        self.message_count = 0
        
    async def send_message(self, message: Dict[str, Any]) -> bool:
        """Send a message to the WebSocket client."""
        try:
            if self.websocket.closed:
                return False
            
            await self.websocket.send_str(json.dumps(message))
            self.message_count += 1
            return True
        except Exception as e:
            logger.error(f"❌ Error sending WebSocket message: {e}")
            return False
    
    def is_subscribed(self, symbol: str) -> bool:
        """Check if connection is subscribed to a symbol."""
        return symbol in self.subscriptions or '*' in self.subscriptions
    
    def add_subscription(self, symbol: str):
        """Add a symbol subscription."""
        self.subscriptions.add(symbol)
    
    def remove_subscription(self, symbol: str):
        """Remove a symbol subscription."""
        self.subscriptions.discard(symbol)

class MarketDataWebSocketStreamer:
    """Professional WebSocket streamer for real-time market data."""
    
    def __init__(self, market_data_manager: AdvancedMarketDataManager):
        self.market_data_manager = market_data_manager
        self.connections: Dict[str, WebSocketConnection] = {}
        self.connection_count = 0
        self.total_messages_sent = 0
        
        # Subscription tracking
        self.symbol_subscribers: Dict[str, Set[str]] = {}
        
        # Performance metrics
        self.start_time = datetime.now()
        self.heartbeat_interval = 30  # 30 seconds
        
        logger.info("🌐 WebSocket Streamer initialized")

    async def setup_websocket_routes(self, app: web.Application):
        """Setup WebSocket routes."""
        app.router.add_get('/ws/market', self.handle_market_websocket)
        app.router.add_get('/ws/market/{symbol}', self.handle_symbol_websocket)
        
        # Subscribe to market data updates
        await self.market_data_manager.subscribe(self._on_market_data_update)
        
        # Start heartbeat task
        asyncio.create_task(self._heartbeat_loop())
        
        logger.info("✅ WebSocket routes configured")

    async def handle_market_websocket(self, request: web.Request) -> web.WebSocketResponse:
        """Handle general market data WebSocket connections."""
        ws = web.WebSocketResponse(heartbeat=30)
        await ws.prepare(request)
        
        # Get user info from session if available
        user_id = None
        if 'user' in request:
            user_id = request['user'].get('user_id')
        
        # Create connection
        connection_id = f"conn_{self.connection_count}"
        self.connection_count += 1
        
        connection = WebSocketConnection(ws, user_id)
        self.connections[connection_id] = connection
        
        logger.info(f"🔌 WebSocket connected: {connection_id} (user: {user_id})")
        
        # Send welcome message
        await connection.send_message({
            'type': 'welcome',
            'connection_id': connection_id,
            'server_time': datetime.now().isoformat(),
            'available_symbols': list(self.market_data_manager.symbols),
            'message': 'Connected to Money Circle Market Data Stream'
        })
        
        try:
            async for msg in ws:
                if msg.type == WSMsgType.TEXT:
                    try:
                        data = json.loads(msg.data)
                        await self._handle_websocket_message(connection_id, data)
                    except json.JSONDecodeError:
                        await connection.send_message({
                            'type': 'error',
                            'message': 'Invalid JSON format'
                        })
                elif msg.type == WSMsgType.ERROR:
                    logger.error(f'WebSocket error: {ws.exception()}')
                    break
        except Exception as e:
            logger.error(f"❌ WebSocket error for {connection_id}: {e}")
        finally:
            # Clean up connection
            await self._cleanup_connection(connection_id)
            logger.info(f"🔌 WebSocket disconnected: {connection_id}")
        
        return ws

    async def handle_symbol_websocket(self, request: web.Request) -> web.WebSocketResponse:
        """Handle symbol-specific WebSocket connections."""
        symbol = request.match_info['symbol'].upper()
        
        # Convert URL format to standard format
        if '/' not in symbol and symbol.endswith('USDT'):
            base = symbol[:-4]
            symbol = f"{base}/USDT"
        
        ws = web.WebSocketResponse(heartbeat=30)
        await ws.prepare(request)
        
        # Get user info from session if available
        user_id = None
        if 'user' in request:
            user_id = request['user'].get('user_id')
        
        # Create connection
        connection_id = f"symbol_{symbol}_{self.connection_count}"
        self.connection_count += 1
        
        connection = WebSocketConnection(ws, user_id)
        connection.add_subscription(symbol)
        self.connections[connection_id] = connection
        
        # Track symbol subscribers
        if symbol not in self.symbol_subscribers:
            self.symbol_subscribers[symbol] = set()
        self.symbol_subscribers[symbol].add(connection_id)
        
        logger.info(f"🔌 Symbol WebSocket connected: {connection_id} for {symbol}")
        
        # Send welcome message with current data
        current_tick = self.market_data_manager.get_market_tick(symbol)
        welcome_data = {
            'type': 'welcome',
            'connection_id': connection_id,
            'symbol': symbol,
            'server_time': datetime.now().isoformat(),
            'message': f'Connected to {symbol} data stream'
        }
        
        if current_tick:
            welcome_data['current_data'] = self._format_tick_data(current_tick)
        
        await connection.send_message(welcome_data)
        
        try:
            async for msg in ws:
                if msg.type == WSMsgType.TEXT:
                    try:
                        data = json.loads(msg.data)
                        await self._handle_websocket_message(connection_id, data)
                    except json.JSONDecodeError:
                        await connection.send_message({
                            'type': 'error',
                            'message': 'Invalid JSON format'
                        })
                elif msg.type == WSMsgType.ERROR:
                    logger.error(f'WebSocket error: {ws.exception()}')
                    break
        except Exception as e:
            logger.error(f"❌ WebSocket error for {connection_id}: {e}")
        finally:
            # Clean up connection
            await self._cleanup_connection(connection_id)
            logger.info(f"🔌 WebSocket disconnected: {connection_id}")
        
        return ws

    async def _handle_websocket_message(self, connection_id: str, data: Dict[str, Any]):
        """Handle incoming WebSocket messages."""
        try:
            connection = self.connections.get(connection_id)
            if not connection:
                return
            
            message_type = data.get('type')
            
            if message_type == 'subscribe':
                await self._handle_subscribe(connection_id, data)
            elif message_type == 'unsubscribe':
                await self._handle_unsubscribe(connection_id, data)
            elif message_type == 'ping':
                await self._handle_ping(connection_id)
            elif message_type == 'get_status':
                await self._handle_get_status(connection_id)
            else:
                await connection.send_message({
                    'type': 'error',
                    'message': f'Unknown message type: {message_type}'
                })
                
        except Exception as e:
            logger.error(f"❌ Error handling WebSocket message: {e}")

    async def _handle_subscribe(self, connection_id: str, data: Dict[str, Any]):
        """Handle subscription requests."""
        try:
            connection = self.connections.get(connection_id)
            if not connection:
                return
            
            symbols = data.get('symbols', [])
            if isinstance(symbols, str):
                symbols = [symbols]
            
            for symbol in symbols:
                symbol = symbol.upper()
                
                # Convert URL format to standard format
                if '/' not in symbol and symbol.endswith('USDT'):
                    base = symbol[:-4]
                    symbol = f"{base}/USDT"
                
                if symbol in self.market_data_manager.symbols or symbol == '*':
                    connection.add_subscription(symbol)
                    
                    # Track symbol subscribers
                    if symbol not in self.symbol_subscribers:
                        self.symbol_subscribers[symbol] = set()
                    self.symbol_subscribers[symbol].add(connection_id)
                    
                    # Send current data if available
                    if symbol != '*':
                        current_tick = self.market_data_manager.get_market_tick(symbol)
                        if current_tick:
                            await connection.send_message({
                                'type': 'ticker',
                                'symbol': symbol,
                                'data': self._format_tick_data(current_tick)
                            })
                    
                    await connection.send_message({
                        'type': 'subscribed',
                        'symbol': symbol,
                        'message': f'Subscribed to {symbol}'
                    })
                else:
                    await connection.send_message({
                        'type': 'error',
                        'message': f'Symbol {symbol} not available'
                    })
                    
        except Exception as e:
            logger.error(f"❌ Error handling subscribe: {e}")

    async def _handle_unsubscribe(self, connection_id: str, data: Dict[str, Any]):
        """Handle unsubscription requests."""
        try:
            connection = self.connections.get(connection_id)
            if not connection:
                return
            
            symbols = data.get('symbols', [])
            if isinstance(symbols, str):
                symbols = [symbols]
            
            for symbol in symbols:
                symbol = symbol.upper()
                
                # Convert URL format to standard format
                if '/' not in symbol and symbol.endswith('USDT'):
                    base = symbol[:-4]
                    symbol = f"{base}/USDT"
                
                connection.remove_subscription(symbol)
                
                # Remove from symbol subscribers
                if symbol in self.symbol_subscribers:
                    self.symbol_subscribers[symbol].discard(connection_id)
                    if not self.symbol_subscribers[symbol]:
                        del self.symbol_subscribers[symbol]
                
                await connection.send_message({
                    'type': 'unsubscribed',
                    'symbol': symbol,
                    'message': f'Unsubscribed from {symbol}'
                })
                
        except Exception as e:
            logger.error(f"❌ Error handling unsubscribe: {e}")

    async def _handle_ping(self, connection_id: str):
        """Handle ping messages."""
        try:
            connection = self.connections.get(connection_id)
            if not connection:
                return
            
            connection.last_ping = datetime.now()
            await connection.send_message({
                'type': 'pong',
                'server_time': datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"❌ Error handling ping: {e}")

    async def _handle_get_status(self, connection_id: str):
        """Handle status requests."""
        try:
            connection = self.connections.get(connection_id)
            if not connection:
                return
            
            uptime = (datetime.now() - connection.connected_at).total_seconds()
            
            await connection.send_message({
                'type': 'status',
                'connection': {
                    'id': connection_id,
                    'uptime_seconds': round(uptime, 1),
                    'messages_sent': connection.message_count,
                    'subscriptions': list(connection.subscriptions)
                },
                'server': {
                    'total_connections': len(self.connections),
                    'total_messages_sent': self.total_messages_sent,
                    'uptime_seconds': (datetime.now() - self.start_time).total_seconds()
                }
            })
            
        except Exception as e:
            logger.error(f"❌ Error handling get_status: {e}")

    async def _on_market_data_update(self, symbol: str, tick: MarketTick):
        """Handle market data updates from the manager."""
        try:
            # Format tick data
            tick_data = self._format_tick_data(tick)
            
            # Send to all subscribers
            message = {
                'type': 'ticker',
                'symbol': symbol,
                'data': tick_data
            }
            
            # Send to symbol-specific subscribers
            symbol_connections = self.symbol_subscribers.get(symbol, set())
            
            # Send to wildcard subscribers
            wildcard_connections = self.symbol_subscribers.get('*', set())
            
            # Combine all relevant connections
            all_connections = symbol_connections | wildcard_connections
            
            # Send messages
            for connection_id in all_connections:
                connection = self.connections.get(connection_id)
                if connection and connection.is_subscribed(symbol):
                    success = await connection.send_message(message)
                    if success:
                        self.total_messages_sent += 1
                    else:
                        # Connection failed, mark for cleanup
                        asyncio.create_task(self._cleanup_connection(connection_id))
                        
        except Exception as e:
            logger.error(f"❌ Error broadcasting market data update: {e}")

    def _format_tick_data(self, tick: MarketTick) -> Dict[str, Any]:
        """Format tick data for WebSocket transmission."""
        return {
            'price': tick.price,
            'bid': tick.bid,
            'ask': tick.ask,
            'volume_24h': tick.volume_24h,
            'change_24h': round(tick.change_24h, 4),
            'change_24h_percent': round(tick.change_24h_percent, 2),
            'high_24h': tick.high_24h,
            'low_24h': tick.low_24h,
            'timestamp': tick.timestamp.isoformat(),
            'source': tick.source.value,
            'quality': tick.quality.value,
            'latency_ms': tick.latency_ms
        }

    async def _cleanup_connection(self, connection_id: str):
        """Clean up a WebSocket connection."""
        try:
            connection = self.connections.get(connection_id)
            if not connection:
                return
            
            # Remove from all symbol subscriptions
            for symbol in list(connection.subscriptions):
                if symbol in self.symbol_subscribers:
                    self.symbol_subscribers[symbol].discard(connection_id)
                    if not self.symbol_subscribers[symbol]:
                        del self.symbol_subscribers[symbol]
            
            # Remove connection
            del self.connections[connection_id]
            
        except Exception as e:
            logger.error(f"❌ Error cleaning up connection {connection_id}: {e}")

    async def _heartbeat_loop(self):
        """Send periodic heartbeat to detect stale connections."""
        while True:
            try:
                await asyncio.sleep(self.heartbeat_interval)
                
                current_time = datetime.now()
                stale_connections = []
                
                for connection_id, connection in self.connections.items():
                    # Check if connection is stale (no ping in 2x heartbeat interval)
                    time_since_ping = (current_time - connection.last_ping).total_seconds()
                    
                    if time_since_ping > self.heartbeat_interval * 2:
                        stale_connections.append(connection_id)
                    else:
                        # Send heartbeat
                        await connection.send_message({
                            'type': 'heartbeat',
                            'server_time': current_time.isoformat()
                        })
                
                # Clean up stale connections
                for connection_id in stale_connections:
                    logger.info(f"🧹 Cleaning up stale connection: {connection_id}")
                    await self._cleanup_connection(connection_id)
                
            except Exception as e:
                logger.error(f"❌ Error in heartbeat loop: {e}")
                await asyncio.sleep(10)

    def get_connection_stats(self) -> Dict[str, Any]:
        """Get WebSocket connection statistics."""
        return {
            'total_connections': len(self.connections),
            'total_messages_sent': self.total_messages_sent,
            'symbol_subscriptions': {
                symbol: len(connections) 
                for symbol, connections in self.symbol_subscribers.items()
            },
            'uptime_seconds': (datetime.now() - self.start_time).total_seconds()
        }
