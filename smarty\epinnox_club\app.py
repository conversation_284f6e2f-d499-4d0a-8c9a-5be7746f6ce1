#!/usr/bin/env python3
"""
Money Circle Investment Club Platform
Main application entry point for the multi-user trading platform.
"""

import asyncio
import logging
import sys
import os
from pathlib import Path
from aiohttp import web, WSMsgType
import aiohttp_cors
from aiohttp_session import setup
from aiohttp_session.cookie_storage import EncryptedCookieStorage
import aiohttp_jinja2
import jinja2

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from config import get_config
from database.models import DatabaseManager
from auth.user_manager import UserManager
from auth.decorators import AuthMiddleware
from exchanges.account_manager import ExchangeAccountManager
from dashboards.personal_dashboard import PersonalDashboard

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/money_circle.log')
    ]
)
logger = logging.getLogger(__name__)

class MoneyCircleApp:
    """Main Money Circle application."""

    def __init__(self, config_name: str = None):
        self.config = get_config(config_name)
        self.config.init_directories()

        # Initialize core components
        self.db_manager = DatabaseManager(self.config.DATABASE_PATH)
        self.user_manager = UserManager(self.db_manager)
        self.exchange_manager = ExchangeAccountManager(self.db_manager)
        self.personal_dashboard = PersonalDashboard(
            self.db_manager,
            self.user_manager,
            self.exchange_manager
        )

        # WebSocket connections
        self.websockets = set()

        logger.info(f"🚀 Money Circle initialized with config: {config_name or 'default'}")

    async def create_app(self) -> web.Application:
        """Create and configure the web application."""
        app = web.Application()

        # Setup session middleware with encryption
        from cryptography.fernet import Fernet
        import base64

        # Generate a proper Fernet key for session storage
        try:
            if isinstance(self.config.SECRET_KEY, str):
                # Convert hex string to bytes and pad/truncate to 32 bytes
                key_bytes = bytes.fromhex(self.config.SECRET_KEY)[:32]
                key_bytes = key_bytes.ljust(32, b'\0')  # Pad with zeros if needed
                secret_key = base64.urlsafe_b64encode(key_bytes)
            else:
                secret_key = base64.urlsafe_b64encode(self.config.SECRET_KEY[:32].ljust(32, b'\0'))
        except Exception:
            # Fallback: generate a new Fernet key
            secret_key = Fernet.generate_key()

        setup(app, EncryptedCookieStorage(secret_key))

        # Setup Jinja2 templates
        aiohttp_jinja2.setup(
            app,
            loader=jinja2.FileSystemLoader('templates')
        )

        # Setup CORS
        cors = aiohttp_cors.setup(app, defaults={
            "*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
                allow_methods="*"
            )
        })

        # Setup authentication middleware
        auth_middleware = AuthMiddleware(self.user_manager)
        app.middlewares.append(auth_middleware)

        # Setup routes
        self._setup_routes(app)

        # Add CORS to all routes
        for route in list(app.router.routes()):
            cors.add(route)

        # Static files
        app.router.add_static('/static/', path='static', name='static')

        logger.info("✅ Web application configured")
        return app

    def _setup_routes(self, app: web.Application):
        """Setup application routes."""
        # Authentication routes
        app.router.add_get('/', self.redirect_to_dashboard)
        app.router.add_get('/login', self.serve_login)
        app.router.add_post('/login', self.handle_login)
        app.router.add_get('/logout', self.handle_logout)
        app.router.add_get('/register', self.serve_register)
        app.router.add_post('/register', self.handle_register)

        # Dashboard routes
        app.router.add_get('/dashboard', self.personal_dashboard.serve_personal_dashboard)
        app.router.add_get('/personal', self.personal_dashboard.serve_personal_dashboard)

        # API routes
        app.router.add_get('/api/portfolio', self.api_get_portfolio)
        app.router.add_post('/api/exchange/add', self.api_add_exchange)
        app.router.add_delete('/api/exchange/{exchange_id}', self.api_remove_exchange)
        app.router.add_get('/api/balance/{exchange_name}', self.api_get_balance)
        app.router.add_get('/api/positions', self.api_get_positions)
        app.router.add_post('/api/order', self.api_place_order)

        # WebSocket
        app.router.add_get('/ws', self.websocket_handler)

        # Admin routes (if admin)
        app.router.add_get('/admin', self.serve_admin_dashboard)
        app.router.add_get('/api/admin/users', self.api_get_users)
        app.router.add_post('/api/admin/user/{user_id}/role', self.api_update_user_role)

        logger.info("✅ Routes configured")

    async def redirect_to_dashboard(self, request: web.Request) -> web.Response:
        """Redirect root to dashboard."""
        return web.Response(status=302, headers={'Location': '/dashboard'})

    async def serve_login(self, request: web.Request) -> web.Response:
        """Serve login page."""
        html_content = """
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Money Circle - Login</title>
            <link rel="stylesheet" href="/static/css/auth.css">
        </head>
        <body>
            <div class="auth-container">
                <div class="auth-card">
                    <h1>💰 Money Circle</h1>
                    <h2>Epinnox Investment Club</h2>
                    <form method="post" action="/login">
                        <div class="form-group">
                            <label for="username">Username</label>
                            <input type="text" id="username" name="username" required>
                        </div>
                        <div class="form-group">
                            <label for="password">Password</label>
                            <input type="password" id="password" name="password" required>
                        </div>
                        <button type="submit" class="auth-btn">Login</button>
                    </form>
                    <p><a href="/register">Don't have an account? Register here</a></p>
                </div>
            </div>
        </body>
        </html>
        """
        return web.Response(text=html_content, content_type='text/html')

    async def handle_login(self, request: web.Request) -> web.Response:
        """Handle login form submission."""
        try:
            data = await request.post()
            username = data.get('username', '').strip()
            password = data.get('password', '')

            if not username or not password:
                return web.Response(status=302, headers={'Location': '/login?error=missing_fields'})

            # Get client IP for rate limiting
            ip_address = request.remote or '127.0.0.1'

            # Authenticate user
            user = self.user_manager.authenticate_user(username, password, ip_address)
            if not user:
                return web.Response(status=302, headers={'Location': '/login?error=invalid_credentials'})

            # Create session
            session_id = self.user_manager.create_session(user)

            # Set session cookie and redirect
            response = web.Response(status=302, headers={'Location': '/dashboard'})
            response.set_cookie(
                'session_id',
                session_id,
                max_age=self.config.SESSION_TIMEOUT,
                httponly=True,
                secure=not self.config.DEBUG
            )

            logger.info(f"✅ User logged in: {username}")
            return response

        except Exception as e:
            logger.error(f"Login error: {e}")
            return web.Response(status=302, headers={'Location': '/login?error=server_error'})

    async def handle_logout(self, request: web.Request) -> web.Response:
        """Handle logout."""
        session_id = request.cookies.get('session_id')
        if session_id:
            self.user_manager.destroy_session(session_id)

        response = web.Response(status=302, headers={'Location': '/login'})
        response.del_cookie('session_id')
        return response

    async def serve_register(self, request: web.Request) -> web.Response:
        """Serve registration page."""
        # Registration HTML would go here
        return web.Response(text="Registration page - Coming soon!", content_type='text/html')

    async def handle_register(self, request: web.Request) -> web.Response:
        """Handle registration form submission."""
        # Registration logic would go here
        return web.Response(status=302, headers={'Location': '/login'})

    async def websocket_handler(self, request: web.Request) -> web.WebSocketResponse:
        """Handle WebSocket connections."""
        ws = web.WebSocketResponse()
        await ws.prepare(request)

        self.websockets.add(ws)
        logger.info(f"📡 WebSocket connected (total: {len(self.websockets)})")

        try:
            async for msg in ws:
                if msg.type == WSMsgType.TEXT:
                    # Handle incoming messages
                    pass
                elif msg.type == WSMsgType.ERROR:
                    logger.error(f'WebSocket error: {ws.exception()}')
        except Exception as e:
            logger.error(f"WebSocket error: {e}")
        finally:
            self.websockets.discard(ws)
            logger.info(f"📡 WebSocket disconnected (total: {len(self.websockets)})")

        return ws

    # API endpoints would be implemented here
    async def api_get_portfolio(self, request: web.Request) -> web.Response:
        """API endpoint to get user portfolio."""
        return web.json_response({"message": "Portfolio API - Coming soon!"})

    async def api_add_exchange(self, request: web.Request) -> web.Response:
        """API endpoint to add exchange account."""
        return web.json_response({"message": "Add exchange API - Coming soon!"})

    async def api_remove_exchange(self, request: web.Request) -> web.Response:
        """API endpoint to remove exchange account."""
        return web.json_response({"message": "Remove exchange API - Coming soon!"})

    async def api_get_balance(self, request: web.Request) -> web.Response:
        """API endpoint to get exchange balance."""
        return web.json_response({"message": "Balance API - Coming soon!"})

    async def api_get_positions(self, request: web.Request) -> web.Response:
        """API endpoint to get user positions."""
        return web.json_response({"message": "Positions API - Coming soon!"})

    async def api_place_order(self, request: web.Request) -> web.Response:
        """API endpoint to place order."""
        return web.json_response({"message": "Place order API - Coming soon!"})

    async def serve_admin_dashboard(self, request: web.Request) -> web.Response:
        """Serve admin dashboard."""
        return web.Response(text="Admin dashboard - Coming soon!", content_type='text/html')

    async def api_get_users(self, request: web.Request) -> web.Response:
        """API endpoint to get all users (admin only)."""
        return web.json_response({"message": "Users API - Coming soon!"})

    async def api_update_user_role(self, request: web.Request) -> web.Response:
        """API endpoint to update user role (admin only)."""
        return web.json_response({"message": "Update role API - Coming soon!"})

    async def start_server(self):
        """Start the Money Circle server."""
        app = await self.create_app()

        runner = web.AppRunner(app)
        await runner.setup()

        site = web.TCPSite(runner, self.config.HOST, self.config.PORT)
        await site.start()

        logger.info(f"🌐 Money Circle running at http://{self.config.HOST}:{self.config.PORT}")
        logger.info(f"🎯 Platform ready for Epinnox investment club members")

        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("🛑 Money Circle shutting down...")
        finally:
            await runner.cleanup()

async def main():
    """Main entry point."""
    config_name = os.getenv('FLASK_ENV', 'development')
    app = MoneyCircleApp(config_name)
    await app.start_server()

if __name__ == '__main__':
    asyncio.run(main())
