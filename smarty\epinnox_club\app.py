#!/usr/bin/env python3
"""
Money Circle Investment Club Platform
Main application entry point for the multi-user trading platform.
"""

import asyncio
import json
import logging
import sys
import os
from pathlib import Path
from typing import Callable
from datetime import datetime
from aiohttp import web, WSMsgType
import aiohttp_cors
from aiohttp_session import setup
from aiohttp_session.cookie_storage import EncryptedCookieStorage
import aiohttp_jinja2
import jinja2

# Add current directory to path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from config import get_config
from database.models import DatabaseManager
from database.club_models import ClubDatabaseManager
from auth.user_manager import UserManager
from auth.decorators import AuthMiddleware
from exchanges.account_manager import ExchangeAccountManager
from dashboards.personal_dashboard import PersonalDashboard
from dashboards.club_dashboard import ClubDashboard
from club.strategy_governance import StrategyGovernance
from club.social_trading import SocialTrading
from club.analytics import ClubAnalytics

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/money_circle.log')
    ]
)
logger = logging.getLogger(__name__)

class MoneyCircleApp:
    """Main Money Circle application."""

    def __init__(self, config_name: str = None):
        self.config = get_config(config_name)
        self.config.init_directories()

        # Initialize core components
        self.db_manager = DatabaseManager(self.config.DATABASE_PATH)
        self.club_db_manager = ClubDatabaseManager(self.db_manager)
        self.user_manager = UserManager(self.db_manager)
        self.exchange_manager = ExchangeAccountManager(self.db_manager)

        # Initialize dashboards
        self.personal_dashboard = PersonalDashboard(
            self.db_manager,
            self.user_manager,
            self.exchange_manager
        )
        self.club_dashboard = ClubDashboard(self.db_manager)

        # Initialize club features
        self.strategy_governance = StrategyGovernance(self.db_manager)
        self.social_trading = SocialTrading(self.db_manager)
        self.club_analytics = ClubAnalytics(self.db_manager)

        # WebSocket connections
        self.websockets = set()

        logger.info(f"🚀 Money Circle initialized with config: {config_name or 'default'}")

    async def create_app(self) -> web.Application:
        """Create and configure the web application."""
        app = web.Application()

        # Setup session middleware with encryption
        # aiohttp_session expects a 32-byte key, not base64 encoded
        if isinstance(self.config.SECRET_KEY, str):
            # Convert hex string to bytes and pad/truncate to 32 bytes
            secret_key = bytes.fromhex(self.config.SECRET_KEY)[:32]
            secret_key = secret_key.ljust(32, b'\0')  # Pad with zeros if needed
        else:
            secret_key = self.config.SECRET_KEY[:32].ljust(32, b'\0')

        setup(app, EncryptedCookieStorage(secret_key))

        # Setup Jinja2 templates
        template_path = Path(__file__).parent / 'templates'
        self.jinja_env = jinja2.Environment(
            loader=jinja2.FileSystemLoader(str(template_path)),
            autoescape=jinja2.select_autoescape(['html', 'xml'])
        )
        aiohttp_jinja2.setup(
            app,
            loader=jinja2.FileSystemLoader(str(template_path))
        )

        # Setup CORS
        cors = aiohttp_cors.setup(app, defaults={
            "*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
                allow_methods="*"
            )
        })

        # Setup authentication middleware
        @web.middleware
        async def auth_middleware(request: web.Request, handler: Callable) -> web.Response:
            """Authentication middleware for aiohttp."""
            path = request.path

            # Public routes that don't require authentication
            public_routes = ['/login', '/logout', '/register', '/agreement', '/welcome', '/static/', '/favicon.ico', '/health']

            # Skip authentication for public routes
            if any(path.startswith(route) for route in public_routes):
                return await handler(request)

            # Check for session
            session_id = request.cookies.get('session_id')
            if session_id:
                session = self.user_manager.validate_session(session_id)
                if session:
                    request['user'] = session

                    # Check if user needs to complete onboarding
                    user = self.user_manager.get_user_by_id(session['user_id'])
                    if user and not user.agreement_accepted and path not in ['/agreement', '/welcome', '/logout']:
                        return web.Response(status=302, headers={'Location': '/agreement'})

                    return await handler(request)

            # Redirect to login for protected routes
            if request.method == 'GET' and not path.startswith('/api/'):
                return web.Response(status=302, headers={'Location': '/login'})
            else:
                # Return 401 for API routes
                return web.json_response({'error': 'Authentication required'}, status=401)

        app.middlewares.append(auth_middleware)

        # Setup routes
        self._setup_routes(app)

        # Add CORS to all routes
        for route in list(app.router.routes()):
            cors.add(route)

        # Static files
        static_path = Path(__file__).parent / 'static'
        app.router.add_static('/static/', path=str(static_path), name='static')

        logger.info("✅ Web application configured")
        return app

    def _setup_routes(self, app: web.Application):
        """Setup application routes."""
        # Authentication routes
        app.router.add_get('/', self.redirect_to_dashboard)
        app.router.add_get('/login', self.serve_login)
        app.router.add_post('/login', self.handle_login)
        app.router.add_get('/logout', self.handle_logout)
        app.router.add_get('/register', self.serve_register)
        app.router.add_post('/register', self.handle_register)
        app.router.add_get('/agreement', self.serve_agreement)
        app.router.add_post('/agreement', self.handle_agreement)
        app.router.add_get('/welcome', self.serve_welcome)

        # Dashboard routes
        app.router.add_get('/dashboard', self.personal_dashboard.serve_personal_dashboard)
        app.router.add_get('/personal', self.personal_dashboard.serve_personal_dashboard)

        # Club routes
        app.router.add_get('/club', self.club_dashboard.serve_club_dashboard)
        app.router.add_get('/club/strategies', self.serve_strategy_marketplace)
        app.router.add_get('/club/members', self.serve_member_directory)
        app.router.add_get('/club/analytics', self.serve_club_analytics)

        # API routes
        app.router.add_get('/api/portfolio', self.api_get_portfolio)
        app.router.add_post('/api/exchange/add', self.api_add_exchange)
        app.router.add_delete('/api/exchange/{exchange_id}', self.api_remove_exchange)
        app.router.add_get('/api/balance/{exchange_name}', self.api_get_balance)
        app.router.add_get('/api/positions', self.api_get_positions)
        app.router.add_post('/api/order', self.api_place_order)

        # Trading API routes
        app.router.add_post('/api/trading/market_order', self.api_place_market_order)
        app.router.add_post('/api/trading/limit_order', self.api_place_limit_order)
        app.router.add_post('/api/trading/close_position', self.api_close_position)
        app.router.add_post('/api/trading/stop_loss', self.api_set_stop_loss)
        app.router.add_post('/api/trading/take_profit', self.api_set_take_profit)
        app.router.add_get('/api/trading/trades', self.api_get_user_trades)

        # Market data API routes
        app.router.add_get('/api/market/data/{symbol}', self.api_get_market_data)
        app.router.add_get('/api/market/orderbook/{symbol}', self.api_get_orderbook)
        app.router.add_get('/api/market/trades/{symbol}', self.api_get_recent_trades)

        # Club API routes
        # Strategy governance
        app.router.add_post('/api/club/strategy/propose', self.api_propose_strategy)
        app.router.add_post('/api/club/strategy/vote', self.api_cast_vote)
        app.router.add_post('/api/club/strategy/admin_review', self.api_admin_review_strategy)
        app.router.add_get('/api/club/strategy/{strategy_id}/discussions', self.api_get_strategy_discussions)
        app.router.add_post('/api/club/strategy/{strategy_id}/discuss', self.api_add_strategy_discussion)
        app.router.add_get('/api/club/strategy/{strategy_id}/votes', self.api_get_strategy_votes)

        # Social trading
        app.router.add_post('/api/club/social/follow_strategy', self.api_follow_strategy)
        app.router.add_post('/api/club/social/unfollow_strategy', self.api_unfollow_strategy)
        app.router.add_get('/api/club/social/activity_feed', self.api_get_activity_feed)
        app.router.add_get('/api/club/social/leaderboard', self.api_get_leaderboard)
        app.router.add_get('/api/club/social/member_profile/{user_id}', self.api_get_member_profile)
        app.router.add_post('/api/club/social/update_profile', self.api_update_member_profile)

        # Club analytics
        app.router.add_get('/api/club/analytics/overview', self.api_get_club_overview)
        app.router.add_get('/api/club/analytics/performance', self.api_get_performance_report)
        app.router.add_get('/api/club/analytics/monthly_report', self.api_get_monthly_report)

        # Notifications
        app.router.add_get('/api/club/notifications', self.api_get_notifications)
        app.router.add_post('/api/club/notifications/{notification_id}/read', self.api_mark_notification_read)

        # WebSocket
        app.router.add_get('/ws', self.websocket_handler)

        # Admin routes (if admin)
        app.router.add_get('/admin', self.serve_admin_dashboard)
        app.router.add_get('/api/admin/users', self.api_get_users)
        app.router.add_post('/api/admin/user/{user_id}/role', self.api_update_user_role)

        logger.info("✅ Routes configured")

    async def redirect_to_dashboard(self, request: web.Request) -> web.Response:
        """Redirect root to dashboard."""
        return web.Response(status=302, headers={'Location': '/dashboard'})

    async def serve_login(self, request: web.Request) -> web.Response:
        """Serve login page."""
        html_content = """
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Money Circle - Login</title>
            <link rel="stylesheet" href="/static/css/auth.css">
        </head>
        <body>
            <div class="auth-container">
                <div class="auth-card">
                    <h1>money 💰 Money Circle</h1>
                    <h2>Money Circle Investment Club</h2>
                    <form method="post" action="/login">
                        <div class="form-group">
                            <label for="username">Username</label>
                            <input type="text" id="username" name="username" required>
                        </div>
                        <div class="form-group">
                            <label for="password">Password</label>
                            <input type="password" id="password" name="password" required>
                        </div>
                        <button type="submit" class="auth-btn">Login</button>
                    </form>
                    <p><a href="/register">Don't have an account? Register here</a></p>
                </div>
            </div>
        </body>
        </html>
        """
        return web.Response(text=html_content, content_type='text/html')

    async def handle_login(self, request: web.Request) -> web.Response:
        """Handle login form submission."""
        try:
            data = await request.post()
            username = data.get('username', '').strip()
            password = data.get('password', '')

            if not username or not password:
                return web.Response(status=302, headers={'Location': '/login?error=missing_fields'})

            # Get client IP for rate limiting
            ip_address = request.remote or '127.0.0.1'

            # Authenticate user
            user = self.user_manager.authenticate_user(username, password, ip_address)
            if not user:
                return web.Response(status=302, headers={'Location': '/login?error=invalid_credentials'})

            # Create session
            session_id = self.user_manager.create_session(user)

            # Set session cookie and redirect
            response = web.Response(status=302, headers={'Location': '/dashboard'})
            response.set_cookie(
                'session_id',
                session_id,
                max_age=self.config.SESSION_TIMEOUT,
                httponly=True,
                secure=not self.config.DEBUG
            )

            logger.info(f"✅ User logged in: {username}")
            return response

        except Exception as e:
            logger.error(f"Login error: {e}")
            return web.Response(status=302, headers={'Location': '/login?error=server_error'})

    async def handle_logout(self, request: web.Request) -> web.Response:
        """Handle logout."""
        session_id = request.cookies.get('session_id')
        if session_id:
            self.user_manager.destroy_session(session_id)

        response = web.Response(status=302, headers={'Location': '/login'})
        response.del_cookie('session_id')
        return response

    async def serve_register(self, request: web.Request) -> web.Response:
        """Serve registration page."""
        try:
            # Get error message from query params
            error = request.query.get('error', '')
            error_messages = {
                'missing_fields': 'Please fill in all required fields.',
                'password_mismatch': 'Passwords do not match.',
                'rate_limit': 'Too many registration attempts. Please try again later.',
                'server_error': 'Registration failed. Please try again.'
            }

            error_text = error_messages.get(error, '')

            # Preserve form data on error
            username = request.query.get('username', '')
            email = request.query.get('email', '')

            # Load and render template
            template = self.jinja_env.get_template('register.html')
            html_content = template.render(
                error=error_text,
                username=username,
                email=email
            )

            return web.Response(text=html_content, content_type='text/html')

        except Exception as e:
            logger.error(f"Registration page error: {e}")
            return web.Response(text="Registration temporarily unavailable", status=500)

    async def handle_register(self, request: web.Request) -> web.Response:
        """Handle registration form submission."""
        try:
            data = await request.post()
            username = data.get('username', '').strip()
            email = data.get('email', '').strip()
            password = data.get('password', '')
            confirm_password = data.get('confirm_password', '')

            # Basic validation
            if not all([username, email, password, confirm_password]):
                return web.Response(status=302, headers={
                    'Location': f'/register?error=missing_fields&username={username}&email={email}'
                })

            if password != confirm_password:
                return web.Response(status=302, headers={
                    'Location': f'/register?error=password_mismatch&username={username}&email={email}'
                })

            # Get client IP for rate limiting
            ip_address = request.remote or '127.0.0.1'

            # Register user
            success, message, user = self.user_manager.register_user(username, email, password, ip_address)

            if not success:
                if 'rate limit' in message.lower():
                    error_code = 'rate_limit'
                else:
                    error_code = 'server_error'

                return web.Response(status=302, headers={
                    'Location': f'/register?error={error_code}&username={username}&email={email}'
                })

            # Auto-login the new user
            session_id = self.user_manager.create_session(user)

            # Set session cookie and redirect to agreement
            response = web.Response(status=302, headers={'Location': '/agreement'})
            response.set_cookie(
                'session_id',
                session_id,
                max_age=self.config.SESSION_TIMEOUT,
                httponly=True,
                secure=not self.config.DEBUG
            )

            logger.info(f"✅ New user registered and logged in: {username}")
            return response

        except Exception as e:
            logger.error(f"Registration error: {e}")
            return web.Response(status=302, headers={'Location': '/register?error=server_error'})

    async def serve_agreement(self, request: web.Request) -> web.Response:
        """Serve membership agreement page."""
        try:
            # Check if user is logged in
            session_id = request.cookies.get('session_id')
            if not session_id:
                return web.Response(status=302, headers={'Location': '/login'})

            session = self.user_manager.validate_session(session_id)
            if not session:
                return web.Response(status=302, headers={'Location': '/login'})

            # Get error message from query params
            error = request.query.get('error', '')
            error_messages = {
                'missing_fields': 'Please complete all required fields and checkboxes.',
                'invalid_signature': 'Please enter your full legal name as your digital signature.',
                'server_error': 'Agreement processing failed. Please try again.'
            }

            error_text = error_messages.get(error, '')

            # Load and render template
            template = self.jinja_env.get_template('agreement.html')
            html_content = template.render(
                error=error_text,
                user=session
            )

            return web.Response(text=html_content, content_type='text/html')

        except Exception as e:
            logger.error(f"Agreement page error: {e}")
            return web.Response(text="Agreement page temporarily unavailable", status=500)

    async def handle_agreement(self, request: web.Request) -> web.Response:
        """Handle agreement acceptance."""
        try:
            # Check if user is logged in
            session_id = request.cookies.get('session_id')
            if not session_id:
                return web.Response(status=302, headers={'Location': '/login'})

            session = self.user_manager.validate_session(session_id)
            if not session:
                return web.Response(status=302, headers={'Location': '/login'})

            data = await request.post()

            # Validate required fields
            required_checkboxes = ['agreement_read', 'risk_acknowledgment', 'age_confirmation']
            digital_signature = data.get('digital_signature', '').strip()

            # Check all checkboxes are checked
            for checkbox in required_checkboxes:
                if not data.get(checkbox):
                    return web.Response(status=302, headers={'Location': '/agreement?error=missing_fields'})

            # Validate digital signature
            if not digital_signature or len(digital_signature) < 2:
                return web.Response(status=302, headers={'Location': '/agreement?error=invalid_signature'})

            # Get client info for legal compliance
            ip_address = request.remote or '127.0.0.1'
            user_agent = request.headers.get('User-Agent', '')

            # Create agreement text for storage
            agreement_text = f"""
Money Circle Investment Club Membership Agreement v1.0

Digital Signature: {digital_signature}
Agreement Read: {data.get('agreement_read') == 'on'}
Risk Acknowledgment: {data.get('risk_acknowledgment') == 'on'}
Age Confirmation: {data.get('age_confirmation') == 'on'}

User Agent: {user_agent}
IP Address: {ip_address}
Timestamp: {datetime.now().isoformat()}
            """.strip()

            # Record agreement acceptance
            success = self.user_manager.record_agreement_acceptance(
                user_id=session['user_id'],
                agreement_text=agreement_text,
                ip_address=ip_address,
                user_agent=user_agent
            )

            if not success:
                return web.Response(status=302, headers={'Location': '/agreement?error=server_error'})

            # Update last login
            self.user_manager.update_last_login(session['user_id'])

            logger.info(f"✅ Agreement accepted by user: {session['username']}")

            # Redirect to welcome page
            return web.Response(status=302, headers={'Location': '/welcome'})

        except Exception as e:
            logger.error(f"Agreement processing error: {e}")
            return web.Response(status=302, headers={'Location': '/agreement?error=server_error'})

    async def serve_welcome(self, request: web.Request) -> web.Response:
        """Serve welcome page after successful registration and agreement."""
        try:
            # Check if user is logged in
            session_id = request.cookies.get('session_id')
            if not session_id:
                return web.Response(status=302, headers={'Location': '/login'})

            session = self.user_manager.validate_session(session_id)
            if not session:
                return web.Response(status=302, headers={'Location': '/login'})

            # Get fresh user information from database
            user = self.user_manager.get_user_by_id(session['user_id'])
            if not user:
                return web.Response(status=302, headers={'Location': '/login'})

            # Double-check agreement status from database (fresh data)
            agreement_status = self.user_manager.get_agreement_status(session['user_id'])
            if not agreement_status:
                return web.Response(status=302, headers={'Location': '/agreement'})

            # Load and render template
            template = self.jinja_env.get_template('welcome.html')
            html_content = template.render(
                user=user,
                session=session
            )

            return web.Response(text=html_content, content_type='text/html')

        except Exception as e:
            logger.error(f"Welcome page error: {e}")
            return web.Response(text="Welcome page temporarily unavailable", status=500)

    async def websocket_handler(self, request: web.Request) -> web.WebSocketResponse:
        """Handle WebSocket connections."""
        ws = web.WebSocketResponse()
        await ws.prepare(request)

        self.websockets.add(ws)
        logger.info(f"📡 WebSocket connected (total: {len(self.websockets)})")

        try:
            async for msg in ws:
                if msg.type == WSMsgType.TEXT:
                    # Handle incoming messages
                    pass
                elif msg.type == WSMsgType.ERROR:
                    logger.error(f'WebSocket error: {ws.exception()}')
        except Exception as e:
            logger.error(f"WebSocket error: {e}")
        finally:
            self.websockets.discard(ws)
            logger.info(f"📡 WebSocket disconnected (total: {len(self.websockets)})")

        return ws

    # API endpoints implementation
    async def api_get_portfolio(self, request: web.Request) -> web.Response:
        """API endpoint to get user portfolio."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            user_id = user['user_id']
            portfolio_data = await self.personal_dashboard._get_portfolio_data(user_id)

            return web.json_response({
                'success': True,
                'portfolio': portfolio_data
            })

        except Exception as e:
            logger.error(f"Portfolio API error: {e}")
            return web.json_response({'error': 'Failed to fetch portfolio'}, status=500)

    async def api_add_exchange(self, request: web.Request) -> web.Response:
        """API endpoint to add exchange account."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            data = await request.json()
            user_id = user['user_id']

            # Validate required fields
            required_fields = ['exchange', 'api_key', 'secret_key']
            for field in required_fields:
                if not data.get(field):
                    return web.json_response({'error': f'Missing {field}'}, status=400)

            # Add exchange account
            success = self.exchange_manager.add_exchange_account(
                user_id=user_id,
                exchange_name=data['exchange'],
                api_key=data['api_key'],
                secret_key=data['secret_key'],
                passphrase=data.get('passphrase')
            )

            if success:
                return web.json_response({'success': True, 'message': 'Exchange account added'})
            else:
                return web.json_response({'error': 'Failed to add exchange account'}, status=400)

        except Exception as e:
            logger.error(f"Add exchange API error: {e}")
            return web.json_response({'error': 'Failed to add exchange account'}, status=500)

    async def api_remove_exchange(self, request: web.Request) -> web.Response:
        """API endpoint to remove exchange account."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            exchange_id = int(request.match_info['exchange_id'])
            user_id = user['user_id']

            success = self.exchange_manager.remove_exchange_account(user_id, exchange_id)

            if success:
                return web.json_response({'success': True, 'message': 'Exchange account removed'})
            else:
                return web.json_response({'error': 'Failed to remove exchange account'}, status=400)

        except Exception as e:
            logger.error(f"Remove exchange API error: {e}")
            return web.json_response({'error': 'Failed to remove exchange account'}, status=500)

    async def api_get_balance(self, request: web.Request) -> web.Response:
        """API endpoint to get exchange balance."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            exchange_name = request.match_info['exchange_name']
            user_id = user['user_id']

            balance = self.exchange_manager.get_user_balance(user_id, exchange_name)

            if balance:
                return web.json_response({'success': True, 'balance': balance})
            else:
                return web.json_response({'error': 'Failed to fetch balance'}, status=400)

        except Exception as e:
            logger.error(f"Balance API error: {e}")
            return web.json_response({'error': 'Failed to fetch balance'}, status=500)

    async def api_get_positions(self, request: web.Request) -> web.Response:
        """API endpoint to get user positions."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            user_id = user['user_id']

            # Get positions from all connected exchanges
            exchanges = self.exchange_manager.get_user_exchanges(user_id)
            all_positions = []

            for exchange_account in exchanges:
                positions = self.exchange_manager.get_user_positions(user_id, exchange_account.exchange_name)
                all_positions.extend(positions)

            return web.json_response({'success': True, 'positions': all_positions})

        except Exception as e:
            logger.error(f"Positions API error: {e}")
            return web.json_response({'error': 'Failed to fetch positions'}, status=500)

    async def api_place_order(self, request: web.Request) -> web.Response:
        """API endpoint to place order."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            data = await request.json()
            user_id = user['user_id']

            # Validate required fields
            required_fields = ['exchange', 'symbol', 'side', 'amount', 'order_type']
            for field in required_fields:
                if not data.get(field):
                    return web.json_response({'error': f'Missing {field}'}, status=400)

            # Place order
            order = self.exchange_manager.place_order(
                user_id=user_id,
                exchange_name=data['exchange'],
                symbol=data['symbol'],
                order_type=data['order_type'],
                side=data['side'],
                amount=float(data['amount']),
                price=float(data['price']) if data.get('price') else None
            )

            if order:
                return web.json_response({'success': True, 'order': order})
            else:
                return web.json_response({'error': 'Failed to place order'}, status=400)

        except Exception as e:
            logger.error(f"Place order API error: {e}")
            return web.json_response({'error': 'Failed to place order'}, status=500)

    # Trading API endpoints
    async def api_place_market_order(self, request: web.Request) -> web.Response:
        """API endpoint to place market order."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            data = await request.json()
            user_id = user['user_id']

            # Validate required fields
            required_fields = ['exchange', 'symbol', 'side', 'amount']
            for field in required_fields:
                if not data.get(field):
                    return web.json_response({'error': f'Missing {field}'}, status=400)

            # Place market order through personal trader
            order = await self.personal_dashboard.personal_trader.place_market_order(
                user_id=user_id,
                exchange_name=data['exchange'],
                symbol=data['symbol'],
                side=data['side'],
                amount=float(data['amount']),
                strategy_name=data.get('strategy', 'Manual')
            )

            if order:
                return web.json_response({'success': True, 'order': order})
            else:
                return web.json_response({'error': 'Failed to place market order'}, status=400)

        except Exception as e:
            logger.error(f"Market order API error: {e}")
            return web.json_response({'error': 'Failed to place market order'}, status=500)

    async def api_place_limit_order(self, request: web.Request) -> web.Response:
        """API endpoint to place limit order."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            data = await request.json()
            user_id = user['user_id']

            # Validate required fields
            required_fields = ['exchange', 'symbol', 'side', 'amount', 'price']
            for field in required_fields:
                if not data.get(field):
                    return web.json_response({'error': f'Missing {field}'}, status=400)

            # Place limit order through personal trader
            order = await self.personal_dashboard.personal_trader.place_limit_order(
                user_id=user_id,
                exchange_name=data['exchange'],
                symbol=data['symbol'],
                side=data['side'],
                amount=float(data['amount']),
                price=float(data['price']),
                strategy_name=data.get('strategy', 'Manual')
            )

            if order:
                return web.json_response({'success': True, 'order': order})
            else:
                return web.json_response({'error': 'Failed to place limit order'}, status=400)

        except Exception as e:
            logger.error(f"Limit order API error: {e}")
            return web.json_response({'error': 'Failed to place limit order'}, status=500)

    async def api_close_position(self, request: web.Request) -> web.Response:
        """API endpoint to close position."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            data = await request.json()
            user_id = user['user_id']

            # Validate required fields
            required_fields = ['exchange', 'symbol']
            for field in required_fields:
                if not data.get(field):
                    return web.json_response({'error': f'Missing {field}'}, status=400)

            # Close position through personal trader
            order = await self.personal_dashboard.personal_trader.close_position(
                user_id=user_id,
                exchange_name=data['exchange'],
                symbol=data['symbol'],
                percentage=float(data.get('percentage', 100.0))
            )

            if order:
                return web.json_response({'success': True, 'order': order})
            else:
                return web.json_response({'error': 'Failed to close position'}, status=400)

        except Exception as e:
            logger.error(f"Close position API error: {e}")
            return web.json_response({'error': 'Failed to close position'}, status=500)

    async def api_set_stop_loss(self, request: web.Request) -> web.Response:
        """API endpoint to set stop loss."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            data = await request.json()
            user_id = user['user_id']

            required_fields = ['exchange', 'symbol', 'stop_price']
            for field in required_fields:
                if not data.get(field):
                    return web.json_response({'error': f'Missing {field}'}, status=400)

            success = await self.personal_dashboard.personal_trader.set_stop_loss(
                user_id=user_id,
                exchange_name=data['exchange'],
                symbol=data['symbol'],
                stop_price=float(data['stop_price'])
            )

            if success:
                return web.json_response({'success': True, 'message': 'Stop loss set'})
            else:
                return web.json_response({'error': 'Failed to set stop loss'}, status=400)

        except Exception as e:
            logger.error(f"Stop loss API error: {e}")
            return web.json_response({'error': 'Failed to set stop loss'}, status=500)

    async def api_set_take_profit(self, request: web.Request) -> web.Response:
        """API endpoint to set take profit."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            data = await request.json()
            user_id = user['user_id']

            required_fields = ['exchange', 'symbol', 'target_price']
            for field in required_fields:
                if not data.get(field):
                    return web.json_response({'error': f'Missing {field}'}, status=400)

            success = await self.personal_dashboard.personal_trader.set_take_profit(
                user_id=user_id,
                exchange_name=data['exchange'],
                symbol=data['symbol'],
                target_price=float(data['target_price'])
            )

            if success:
                return web.json_response({'success': True, 'message': 'Take profit set'})
            else:
                return web.json_response({'error': 'Failed to set take profit'}, status=400)

        except Exception as e:
            logger.error(f"Take profit API error: {e}")
            return web.json_response({'error': 'Failed to set take profit'}, status=500)

    async def api_get_user_trades(self, request: web.Request) -> web.Response:
        """API endpoint to get user trades."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            user_id = user['user_id']
            limit = int(request.query.get('limit', 50))

            trades = await self.personal_dashboard.personal_trader.get_user_trades(user_id, limit)

            return web.json_response({'success': True, 'trades': trades})

        except Exception as e:
            logger.error(f"Get trades API error: {e}")
            return web.json_response({'error': 'Failed to get trades'}, status=500)

    # Market data API endpoints
    async def api_get_market_data(self, request: web.Request) -> web.Response:
        """API endpoint to get market data."""
        try:
            symbol = request.match_info['symbol']

            market_data = await self.personal_dashboard.market_data_bus.get_latest_market_data(symbol)

            if market_data:
                return web.json_response({'success': True, 'data': market_data})
            else:
                return web.json_response({'error': 'No market data available'}, status=404)

        except Exception as e:
            logger.error(f"Market data API error: {e}")
            return web.json_response({'error': 'Failed to get market data'}, status=500)

    async def api_get_orderbook(self, request: web.Request) -> web.Response:
        """API endpoint to get order book."""
        try:
            symbol = request.match_info['symbol']

            orderbook = await self.personal_dashboard.market_data_bus.get_latest_orderbook(symbol)

            if orderbook:
                return web.json_response({'success': True, 'orderbook': orderbook})
            else:
                return web.json_response({'error': 'No orderbook data available'}, status=404)

        except Exception as e:
            logger.error(f"Orderbook API error: {e}")
            return web.json_response({'error': 'Failed to get orderbook'}, status=500)

    async def api_get_recent_trades(self, request: web.Request) -> web.Response:
        """API endpoint to get recent trades."""
        try:
            symbol = request.match_info['symbol']
            limit = int(request.query.get('limit', 20))

            trades = await self.personal_dashboard.market_data_bus.get_recent_trades(symbol, limit)

            return web.json_response({'success': True, 'trades': trades})

        except Exception as e:
            logger.error(f"Recent trades API error: {e}")
            return web.json_response({'error': 'Failed to get recent trades'}, status=500)

    async def serve_admin_dashboard(self, request: web.Request) -> web.Response:
        """Serve admin dashboard."""
        return web.Response(text="Admin dashboard - Coming soon!", content_type='text/html')

    async def api_get_users(self, request: web.Request) -> web.Response:
        """API endpoint to get all users (admin only)."""
        return web.json_response({"message": "Users API - Coming soon!"})

    async def api_update_user_role(self, request: web.Request) -> web.Response:
        """API endpoint to update user role (admin only)."""
        return web.json_response({"message": "Update role API - Coming soon!"})

    # Club page routes
    async def serve_strategy_marketplace(self, request: web.Request) -> web.Response:
        """Serve strategy marketplace page."""
        return web.Response(text="Strategy Marketplace - Coming soon!", content_type='text/html')

    async def serve_member_directory(self, request: web.Request) -> web.Response:
        """Serve member directory page."""
        return web.Response(text="Member Directory - Coming soon!", content_type='text/html')

    async def serve_club_analytics(self, request: web.Request) -> web.Response:
        """Serve club analytics page."""
        return web.Response(text="Club Analytics - Coming soon!", content_type='text/html')

    # Strategy Governance API endpoints
    async def api_propose_strategy(self, request: web.Request) -> web.Response:
        """API endpoint to propose new strategy."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            data = await request.json()
            user_id = user['user_id']

            # Validate required fields
            required_fields = ['title', 'description', 'strategy_type', 'risk_level',
                             'expected_return', 'max_drawdown', 'time_horizon']
            for field in required_fields:
                if not data.get(field):
                    return web.json_response({'error': f'Missing {field}'}, status=400)

            # Submit proposal
            strategy_id = self.strategy_governance.submit_strategy_proposal(
                user_id=user_id,
                title=data['title'],
                description=data['description'],
                strategy_type=data['strategy_type'],
                risk_level=data['risk_level'],
                expected_return=float(data['expected_return']),
                max_drawdown=float(data['max_drawdown']),
                time_horizon=data['time_horizon'],
                parameters=json.loads(data.get('parameters', '{}'))
            )

            if strategy_id:
                return web.json_response({'success': True, 'strategy_id': strategy_id})
            else:
                return web.json_response({'error': 'Failed to submit proposal'}, status=400)

        except Exception as e:
            logger.error(f"Propose strategy API error: {e}")
            return web.json_response({'error': 'Failed to submit proposal'}, status=500)

    async def api_cast_vote(self, request: web.Request) -> web.Response:
        """API endpoint to cast vote on strategy."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            data = await request.json()
            user_id = user['user_id']

            # Validate required fields
            required_fields = ['strategy_id', 'vote']
            for field in required_fields:
                if not data.get(field):
                    return web.json_response({'error': f'Missing {field}'}, status=400)

            # Cast vote
            success = self.strategy_governance.cast_vote(
                user_id=user_id,
                strategy_id=int(data['strategy_id']),
                vote=data['vote'],
                reasoning=data.get('reasoning', '')
            )

            if success:
                return web.json_response({'success': True})
            else:
                return web.json_response({'error': 'Failed to cast vote'}, status=400)

        except Exception as e:
            logger.error(f"Cast vote API error: {e}")
            return web.json_response({'error': 'Failed to cast vote'}, status=500)

    async def api_admin_review_strategy(self, request: web.Request) -> web.Response:
        """API endpoint for admin strategy review."""
        try:
            user = request.get('user')
            if not user or user.get('role') != 'admin':
                return web.json_response({'error': 'Admin access required'}, status=403)

            data = await request.json()
            admin_user_id = user['user_id']

            # Validate required fields
            required_fields = ['strategy_id', 'action']
            for field in required_fields:
                if not data.get(field):
                    return web.json_response({'error': f'Missing {field}'}, status=400)

            # Review strategy
            success = self.strategy_governance.admin_review_strategy(
                admin_user_id=admin_user_id,
                strategy_id=int(data['strategy_id']),
                action=data['action'],
                review_notes=data.get('review_notes', '')
            )

            if success:
                return web.json_response({'success': True})
            else:
                return web.json_response({'error': 'Failed to review strategy'}, status=400)

        except Exception as e:
            logger.error(f"Admin review API error: {e}")
            return web.json_response({'error': 'Failed to review strategy'}, status=500)

    async def api_get_strategy_discussions(self, request: web.Request) -> web.Response:
        """API endpoint to get strategy discussions."""
        try:
            strategy_id = int(request.match_info['strategy_id'])
            discussions = self.strategy_governance.get_strategy_discussions(strategy_id)

            return web.json_response({'success': True, 'discussions': discussions})

        except Exception as e:
            logger.error(f"Get discussions API error: {e}")
            return web.json_response({'error': 'Failed to get discussions'}, status=500)

    async def api_add_strategy_discussion(self, request: web.Request) -> web.Response:
        """API endpoint to add strategy discussion."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            strategy_id = int(request.match_info['strategy_id'])
            data = await request.json()
            user_id = user['user_id']

            discussion_id = self.strategy_governance.add_strategy_discussion(
                user_id=user_id,
                strategy_id=strategy_id,
                content=data['content'],
                parent_id=data.get('parent_id')
            )

            if discussion_id:
                return web.json_response({'success': True, 'discussion_id': discussion_id})
            else:
                return web.json_response({'error': 'Failed to add discussion'}, status=400)

        except Exception as e:
            logger.error(f"Add discussion API error: {e}")
            return web.json_response({'error': 'Failed to add discussion'}, status=500)

    async def api_get_strategy_votes(self, request: web.Request) -> web.Response:
        """API endpoint to get strategy votes."""
        try:
            strategy_id = int(request.match_info['strategy_id'])
            votes = self.strategy_governance.get_strategy_votes(strategy_id)

            return web.json_response({'success': True, 'votes': votes})

        except Exception as e:
            logger.error(f"Get votes API error: {e}")
            return web.json_response({'error': 'Failed to get votes'}, status=500)

    # Social Trading API endpoints
    async def api_follow_strategy(self, request: web.Request) -> web.Response:
        """API endpoint to follow strategy."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            data = await request.json()
            user_id = user['user_id']

            success = self.social_trading.follow_strategy(
                user_id=user_id,
                strategy_id=int(data['strategy_id']),
                auto_execute=data.get('auto_execute', False),
                allocation_percentage=float(data.get('allocation_percentage', 0.0))
            )

            if success:
                return web.json_response({'success': True})
            else:
                return web.json_response({'error': 'Failed to follow strategy'}, status=400)

        except Exception as e:
            logger.error(f"Follow strategy API error: {e}")
            return web.json_response({'error': 'Failed to follow strategy'}, status=500)

    async def api_unfollow_strategy(self, request: web.Request) -> web.Response:
        """API endpoint to unfollow strategy."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            data = await request.json()
            user_id = user['user_id']

            success = self.social_trading.unfollow_strategy(
                user_id=user_id,
                strategy_id=int(data['strategy_id'])
            )

            if success:
                return web.json_response({'success': True})
            else:
                return web.json_response({'error': 'Failed to unfollow strategy'}, status=400)

        except Exception as e:
            logger.error(f"Unfollow strategy API error: {e}")
            return web.json_response({'error': 'Failed to unfollow strategy'}, status=500)

    async def api_get_activity_feed(self, request: web.Request) -> web.Response:
        """API endpoint to get activity feed."""
        try:
            user_id = request.query.get('user_id')
            limit = int(request.query.get('limit', 50))

            if user_id:
                user_id = int(user_id)

            activities = self.social_trading.get_member_activity_feed(user_id, limit)

            return web.json_response({'success': True, 'activities': activities})

        except Exception as e:
            logger.error(f"Activity feed API error: {e}")
            return web.json_response({'error': 'Failed to get activity feed'}, status=500)

    async def api_get_leaderboard(self, request: web.Request) -> web.Response:
        """API endpoint to get member leaderboard."""
        try:
            metric = request.query.get('metric', 'reputation')
            period = request.query.get('period', 'all_time')
            limit = int(request.query.get('limit', 20))

            leaderboard = self.social_trading.get_member_leaderboard(metric, period, limit)

            return web.json_response({'success': True, 'leaderboard': leaderboard})

        except Exception as e:
            logger.error(f"Leaderboard API error: {e}")
            return web.json_response({'error': 'Failed to get leaderboard'}, status=500)

    async def api_get_member_profile(self, request: web.Request) -> web.Response:
        """API endpoint to get member profile."""
        try:
            user_id = int(request.match_info['user_id'])
            profile = self.social_trading.get_member_profile(user_id)

            if profile:
                return web.json_response({'success': True, 'profile': profile})
            else:
                return web.json_response({'error': 'Profile not found'}, status=404)

        except Exception as e:
            logger.error(f"Get profile API error: {e}")
            return web.json_response({'error': 'Failed to get profile'}, status=500)

    async def api_update_member_profile(self, request: web.Request) -> web.Response:
        """API endpoint to update member profile."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            data = await request.json()
            user_id = user['user_id']

            success = self.social_trading.update_member_profile(user_id, data)

            if success:
                return web.json_response({'success': True})
            else:
                return web.json_response({'error': 'Failed to update profile'}, status=400)

        except Exception as e:
            logger.error(f"Update profile API error: {e}")
            return web.json_response({'error': 'Failed to update profile'}, status=500)

    # Club Analytics API endpoints
    async def api_get_club_overview(self, request: web.Request) -> web.Response:
        """API endpoint to get club overview."""
        try:
            overview = self.club_analytics.generate_club_overview()
            return web.json_response({'success': True, 'overview': overview})

        except Exception as e:
            logger.error(f"Club overview API error: {e}")
            return web.json_response({'error': 'Failed to get club overview'}, status=500)

    async def api_get_performance_report(self, request: web.Request) -> web.Response:
        """API endpoint to get performance report."""
        try:
            period_days = int(request.query.get('period_days', 30))
            report = self.club_analytics.generate_performance_report(period_days)

            return web.json_response({'success': True, 'report': report})

        except Exception as e:
            logger.error(f"Performance report API error: {e}")
            return web.json_response({'error': 'Failed to get performance report'}, status=500)

    async def api_get_monthly_report(self, request: web.Request) -> web.Response:
        """API endpoint to get monthly report."""
        try:
            report = self.club_analytics.generate_monthly_report()
            return web.json_response({'success': True, 'report': report})

        except Exception as e:
            logger.error(f"Monthly report API error: {e}")
            return web.json_response({'error': 'Failed to get monthly report'}, status=500)

    # Notifications API endpoints
    async def api_get_notifications(self, request: web.Request) -> web.Response:
        """API endpoint to get user notifications."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            user_id = user['user_id']
            limit = int(request.query.get('limit', 20))

            # Get notifications from database
            cursor = self.db_manager.conn.execute("""
                SELECT id, notification_type, title, content, related_id,
                       is_read, timestamp
                FROM club_notifications
                WHERE user_id = ?
                ORDER BY timestamp DESC
                LIMIT ?
            """, (user_id, limit))

            notifications = []
            for row in cursor.fetchall():
                notifications.append({
                    'id': row[0],
                    'notification_type': row[1],
                    'title': row[2],
                    'content': row[3],
                    'related_id': row[4],
                    'is_read': row[5],
                    'timestamp': row[6]
                })

            return web.json_response({'success': True, 'notifications': notifications})

        except Exception as e:
            logger.error(f"Notifications API error: {e}")
            return web.json_response({'error': 'Failed to get notifications'}, status=500)

    async def api_mark_notification_read(self, request: web.Request) -> web.Response:
        """API endpoint to mark notification as read."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            notification_id = int(request.match_info['notification_id'])
            user_id = user['user_id']

            # Mark notification as read
            self.db_manager.conn.execute("""
                UPDATE club_notifications
                SET is_read = TRUE
                WHERE id = ? AND user_id = ?
            """, (notification_id, user_id))

            self.db_manager.conn.commit()

            return web.json_response({'success': True})

        except Exception as e:
            logger.error(f"Mark notification read API error: {e}")
            return web.json_response({'error': 'Failed to mark notification as read'}, status=500)

    async def start_server(self):
        """Start the Money Circle server."""
        app = await self.create_app()

        runner = web.AppRunner(app)
        await runner.setup()

        site = web.TCPSite(runner, self.config.HOST, self.config.PORT)
        await site.start()

        logger.info(f"🌐 Money Circle running at http://{self.config.HOST}:{self.config.PORT}")
        logger.info(f"🎯 Platform ready for Money Circle investment club members")

        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("🛑 Money Circle shutting down...")
        finally:
            await runner.cleanup()

async def main():
    """Main entry point."""
    config_name = os.getenv('FLASK_ENV', 'development')
    app = MoneyCircleApp(config_name)
    await app.start_server()

if __name__ == '__main__':
    asyncio.run(main())
