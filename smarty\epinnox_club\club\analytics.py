#!/usr/bin/env python3
"""
Money Circle Club Analytics and Reporting
Advanced analytics, performance tracking, and automated reporting.
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from database.models import DatabaseManager
from database.club_models import ClubDatabaseManager

logger = logging.getLogger(__name__)

class ClubAnalytics:
    """Club-wide analytics and reporting system."""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.club_db = ClubDatabaseManager(db_manager)
    
    def generate_club_overview(self) -> Dict[str, Any]:
        """Generate comprehensive club overview."""
        try:
            overview = {}
            
            # Member statistics
            cursor = self.db.conn.execute("""
                SELECT 
                    COUNT(*) as total_members,
                    COUNT(CASE WHEN u.last_login >= DATE('now', '-7 days') THEN 1 END) as active_weekly,
                    COUNT(CASE WHEN u.last_login >= DATE('now', '-30 days') THEN 1 END) as active_monthly
                FROM users u
                WHERE u.role IN ('admin', 'member')
            """)
            
            member_stats = cursor.fetchone()
            overview['members'] = {
                'total': member_stats[0] or 0,
                'active_weekly': member_stats[1] or 0,
                'active_monthly': member_stats[2] or 0
            }
            
            # Strategy statistics
            cursor = self.db.conn.execute("""
                SELECT 
                    COUNT(*) as total_strategies,
                    COUNT(CASE WHEN status = 'approved' AND is_active = TRUE THEN 1 END) as active_strategies,
                    COUNT(CASE WHEN status = 'voting' THEN 1 END) as voting_strategies,
                    COUNT(CASE WHEN status = 'pending_review' THEN 1 END) as pending_strategies
                FROM strategy_proposals
            """)
            
            strategy_stats = cursor.fetchone()
            overview['strategies'] = {
                'total': strategy_stats[0] or 0,
                'active': strategy_stats[1] or 0,
                'voting': strategy_stats[2] or 0,
                'pending': strategy_stats[3] or 0
            }
            
            # Trading statistics
            cursor = self.db.conn.execute("""
                SELECT 
                    COUNT(*) as total_trades,
                    COUNT(CASE WHEN timestamp >= DATE('now', '-7 days') THEN 1 END) as trades_weekly,
                    COUNT(CASE WHEN timestamp >= DATE('now', '-30 days') THEN 1 END) as trades_monthly,
                    SUM(size * price) as total_volume,
                    AVG(size * price) as avg_trade_size
                FROM user_trades
            """)
            
            trade_stats = cursor.fetchone()
            overview['trading'] = {
                'total_trades': trade_stats[0] or 0,
                'trades_weekly': trade_stats[1] or 0,
                'trades_monthly': trade_stats[2] or 0,
                'total_volume': trade_stats[3] or 0.0,
                'avg_trade_size': trade_stats[4] or 0.0
            }
            
            # Assets under management (AUM)
            overview['aum'] = self._calculate_total_aum()
            
            # Recent activity summary
            overview['recent_activity'] = self._get_recent_activity_summary()
            
            return overview
            
        except Exception as e:
            logger.error(f"Error generating club overview: {e}")
            return {}
    
    def generate_performance_report(self, period_days: int = 30) -> Dict[str, Any]:
        """Generate club performance report."""
        try:
            report = {
                'period_days': period_days,
                'generated_at': datetime.now().isoformat()
            }
            
            # Club-wide performance
            cursor = self.db.conn.execute("""
                SELECT 
                    AVG(club_return) as avg_club_return,
                    AVG(avg_member_return) as avg_member_return,
                    SUM(total_trades) as total_trades,
                    AVG(total_aum) as avg_aum,
                    COUNT(*) as data_points
                FROM club_analytics
                WHERE date >= DATE('now', '-{} days')
            """.format(period_days))
            
            club_perf = cursor.fetchone()
            report['club_performance'] = {
                'avg_return': club_perf[0] or 0.0,
                'avg_member_return': club_perf[1] or 0.0,
                'total_trades': club_perf[2] or 0,
                'avg_aum': club_perf[3] or 0.0,
                'data_points': club_perf[4] or 0
            }
            
            # Top performing strategies
            cursor = self.db.conn.execute("""
                SELECT 
                    sp.id, sp.title, sp.strategy_type,
                    AVG(spr.total_return) as avg_return,
                    AVG(spr.win_rate) as avg_win_rate,
                    MAX(spr.followers_count) as max_followers
                FROM strategy_proposals sp
                JOIN strategy_performance spr ON sp.id = spr.strategy_id
                WHERE spr.date >= DATE('now', '-{} days')
                    AND sp.status = 'approved' AND sp.is_active = TRUE
                GROUP BY sp.id, sp.title, sp.strategy_type
                ORDER BY avg_return DESC
                LIMIT 10
            """.format(period_days))
            
            top_strategies = []
            for row in cursor.fetchall():
                top_strategies.append({
                    'strategy_id': row[0],
                    'title': row[1],
                    'strategy_type': row[2],
                    'avg_return': row[3] or 0.0,
                    'avg_win_rate': row[4] or 0.0,
                    'max_followers': row[5] or 0
                })
            
            report['top_strategies'] = top_strategies
            
            # Member engagement metrics
            cursor = self.db.conn.execute("""
                SELECT 
                    COUNT(DISTINCT user_id) as active_members,
                    COUNT(*) as total_activities,
                    COUNT(CASE WHEN activity_type = 'vote' THEN 1 END) as votes,
                    COUNT(CASE WHEN activity_type = 'strategy_proposal' THEN 1 END) as proposals,
                    COUNT(CASE WHEN activity_type = 'comment' THEN 1 END) as comments
                FROM member_activities
                WHERE timestamp >= DATE('now', '-{} days')
            """.format(period_days))
            
            engagement = cursor.fetchone()
            report['engagement'] = {
                'active_members': engagement[0] or 0,
                'total_activities': engagement[1] or 0,
                'votes': engagement[2] or 0,
                'proposals': engagement[3] or 0,
                'comments': engagement[4] or 0
            }
            
            # Risk metrics
            report['risk_metrics'] = self._calculate_risk_metrics(period_days)
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating performance report: {e}")
            return {}
    
    def calculate_member_contribution_score(self, user_id: int) -> float:
        """Calculate member contribution score based on various activities."""
        try:
            score = 0.0
            
            # Strategy proposals (high weight)
            cursor = self.db.conn.execute("""
                SELECT COUNT(*) FROM strategy_proposals 
                WHERE user_id = ? AND status IN ('approved', 'voting')
            """, (user_id,))
            
            proposals = cursor.fetchone()[0] or 0
            score += proposals * 50.0  # 50 points per proposal
            
            # Voting participation (medium weight)
            cursor = self.db.conn.execute("""
                SELECT COUNT(*) FROM strategy_votes WHERE user_id = ?
            """, (user_id,))
            
            votes = cursor.fetchone()[0] or 0
            score += votes * 10.0  # 10 points per vote
            
            # Discussion participation (medium weight)
            cursor = self.db.conn.execute("""
                SELECT COUNT(*) FROM strategy_discussions WHERE user_id = ?
            """, (user_id,))
            
            comments = cursor.fetchone()[0] or 0
            score += comments * 5.0  # 5 points per comment
            
            # Trading activity (low weight)
            cursor = self.db.conn.execute("""
                SELECT COUNT(*) FROM user_trades 
                WHERE user_id = ? AND timestamp >= DATE('now', '-30 days')
            """, (user_id,))
            
            recent_trades = cursor.fetchone()[0] or 0
            score += recent_trades * 1.0  # 1 point per trade
            
            # Strategy following (low weight)
            cursor = self.db.conn.execute("""
                SELECT COUNT(*) FROM strategy_following 
                WHERE user_id = ? AND is_active = TRUE
            """, (user_id,))
            
            following = cursor.fetchone()[0] or 0
            score += following * 2.0  # 2 points per followed strategy
            
            # Update reputation score in database
            self.db.conn.execute("""
                UPDATE member_profiles 
                SET reputation_score = ?
                WHERE user_id = ?
            """, (score, user_id))
            
            self.db.conn.commit()
            
            return score
            
        except Exception as e:
            logger.error(f"Error calculating contribution score: {e}")
            return 0.0
    
    def update_daily_analytics(self):
        """Update daily club analytics."""
        try:
            today = datetime.now().date()
            
            # Calculate daily metrics
            analytics_data = self._calculate_daily_metrics()
            
            # Insert or update today's analytics
            self.db.conn.execute("""
                INSERT OR REPLACE INTO club_analytics 
                (date, total_members, active_members, total_aum, total_strategies,
                 active_strategies, club_return, avg_member_return, total_trades)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                today.isoformat(),
                analytics_data['total_members'],
                analytics_data['active_members'],
                analytics_data['total_aum'],
                analytics_data['total_strategies'],
                analytics_data['active_strategies'],
                analytics_data['club_return'],
                analytics_data['avg_member_return'],
                analytics_data['total_trades']
            ))
            
            self.db.conn.commit()
            logger.info(f"Daily analytics updated for {today}")
            
        except Exception as e:
            logger.error(f"Error updating daily analytics: {e}")
    
    def get_club_analytics_history(self, days: int = 30) -> List[Dict[str, Any]]:
        """Get historical club analytics."""
        try:
            cursor = self.db.conn.execute("""
                SELECT 
                    date, total_members, active_members, total_aum,
                    total_strategies, active_strategies, club_return,
                    avg_member_return, total_trades
                FROM club_analytics
                WHERE date >= DATE('now', '-{} days')
                ORDER BY date ASC
            """.format(days))
            
            history = []
            for row in cursor.fetchall():
                history.append({
                    'date': row[0],
                    'total_members': row[1] or 0,
                    'active_members': row[2] or 0,
                    'total_aum': row[3] or 0.0,
                    'total_strategies': row[4] or 0,
                    'active_strategies': row[5] or 0,
                    'club_return': row[6] or 0.0,
                    'avg_member_return': row[7] or 0.0,
                    'total_trades': row[8] or 0
                })
            
            return history
            
        except Exception as e:
            logger.error(f"Error getting analytics history: {e}")
            return []
    
    def _calculate_total_aum(self) -> float:
        """Calculate total assets under management."""
        try:
            # This would integrate with exchange balances
            # For now, return a placeholder calculation
            cursor = self.db.conn.execute("""
                SELECT COUNT(DISTINCT user_id) FROM user_exchanges
            """)
            
            connected_users = cursor.fetchone()[0] or 0
            # Placeholder: assume average $1000 per connected user
            return connected_users * 1000.0
            
        except Exception as e:
            logger.error(f"Error calculating AUM: {e}")
            return 0.0
    
    def _get_recent_activity_summary(self, days: int = 7) -> Dict[str, int]:
        """Get recent activity summary."""
        try:
            cursor = self.db.conn.execute("""
                SELECT 
                    activity_type,
                    COUNT(*) as count
                FROM member_activities
                WHERE timestamp >= DATE('now', '-{} days')
                GROUP BY activity_type
            """.format(days))
            
            activity_summary = {}
            for row in cursor.fetchall():
                activity_summary[row[0]] = row[1]
            
            return activity_summary
            
        except Exception as e:
            logger.error(f"Error getting activity summary: {e}")
            return {}
    
    def _calculate_risk_metrics(self, period_days: int) -> Dict[str, float]:
        """Calculate club-wide risk metrics."""
        try:
            # Portfolio correlation analysis
            cursor = self.db.conn.execute("""
                SELECT 
                    symbol,
                    COUNT(*) as trade_count,
                    SUM(CASE WHEN side = 'buy' THEN size ELSE -size END) as net_position
                FROM user_trades
                WHERE timestamp >= DATE('now', '-{} days')
                GROUP BY symbol
            """.format(period_days))
            
            positions = {}
            total_exposure = 0.0
            
            for row in cursor.fetchall():
                symbol = row[0]
                net_position = abs(row[2] or 0)
                positions[symbol] = net_position
                total_exposure += net_position
            
            # Calculate concentration risk
            max_concentration = 0.0
            if total_exposure > 0:
                for symbol, position in positions.items():
                    concentration = position / total_exposure
                    max_concentration = max(max_concentration, concentration)
            
            # Calculate strategy diversification
            cursor = self.db.conn.execute("""
                SELECT COUNT(DISTINCT strategy_id) FROM strategy_following
                WHERE is_active = TRUE
            """)
            
            active_strategies = cursor.fetchone()[0] or 0
            
            return {
                'max_concentration': max_concentration,
                'total_exposure': total_exposure,
                'active_strategies': active_strategies,
                'diversification_score': min(active_strategies / 10.0, 1.0)  # Normalized to 1.0
            }
            
        except Exception as e:
            logger.error(f"Error calculating risk metrics: {e}")
            return {}
    
    def _calculate_daily_metrics(self) -> Dict[str, Any]:
        """Calculate daily metrics for analytics."""
        try:
            metrics = {}
            
            # Member counts
            cursor = self.db.conn.execute("""
                SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN last_login >= DATE('now', '-7 days') THEN 1 END) as active
                FROM users
                WHERE role IN ('admin', 'member')
            """)
            
            member_data = cursor.fetchone()
            metrics['total_members'] = member_data[0] or 0
            metrics['active_members'] = member_data[1] or 0
            
            # Strategy counts
            cursor = self.db.conn.execute("""
                SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN status = 'approved' AND is_active = TRUE THEN 1 END) as active
                FROM strategy_proposals
            """)
            
            strategy_data = cursor.fetchone()
            metrics['total_strategies'] = strategy_data[0] or 0
            metrics['active_strategies'] = strategy_data[1] or 0
            
            # AUM and returns (placeholder calculations)
            metrics['total_aum'] = self._calculate_total_aum()
            metrics['club_return'] = 0.0  # Would calculate from actual performance
            metrics['avg_member_return'] = 0.0  # Would calculate from member performance
            
            # Trade count for today
            cursor = self.db.conn.execute("""
                SELECT COUNT(*) FROM user_trades
                WHERE DATE(timestamp) = DATE('now')
            """)
            
            metrics['total_trades'] = cursor.fetchone()[0] or 0
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error calculating daily metrics: {e}")
            return {}
