#!/usr/bin/env python3
"""
Money Circle Strategy Governance System
Handles strategy proposals, voting, and approval workflows.
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import asdict
from database.models import DatabaseManager
from database.club_models import ClubDatabaseManager, StrategyDiscussion, MemberActivity

logger = logging.getLogger(__name__)

class StrategyGovernance:
    """Strategy governance and voting system."""

    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.club_db = ClubDatabaseManager(db_manager)

    def submit_strategy_proposal(self, user_id: int, title: str, description: str,
                               strategy_type: str, risk_level: str, expected_return: float,
                               max_drawdown: float, time_horizon: str,
                               parameters: Dict[str, Any]) -> Optional[int]:
        """Submit a new strategy proposal."""
        try:
            # Insert strategy proposal
            cursor = self.db.conn.execute("""
                INSERT INTO strategy_proposals
                (user_id, title, description, strategy_type, risk_level,
                 expected_return, max_drawdown, time_horizon, parameters, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending_review')
            """, (user_id, title, description, strategy_type, risk_level,
                  expected_return, max_drawdown, time_horizon, json.dumps(parameters)))

            strategy_id = cursor.lastrowid

            # Log member activity
            self._log_member_activity(user_id, 'strategy_proposal', {
                'strategy_id': strategy_id,
                'title': title,
                'strategy_type': strategy_type
            })

            # Notify admins for review
            self._notify_admins_for_review(strategy_id, title)

            self.db.conn.commit()
            logger.info(f"Strategy proposal submitted: {strategy_id} by user {user_id}")

            return strategy_id

        except Exception as e:
            logger.error(f"Error submitting strategy proposal: {e}")
            return None

    def admin_review_strategy(self, admin_user_id: int, strategy_id: int,
                            action: str, review_notes: str = "") -> bool:
        """Admin review of strategy proposal."""
        try:
            if action not in ['approve', 'reject', 'request_changes']:
                return False

            # Update strategy status
            if action == 'approve':
                new_status = 'voting'
                # Set voting deadline
                voting_days = int(self.club_db.get_voting_config('voting_period_days') or '7')
                voting_deadline = datetime.now() + timedelta(days=voting_days)

                self.db.conn.execute("""
                    UPDATE strategy_proposals
                    SET status = ?, admin_review_notes = ?, voting_deadline = ?
                    WHERE id = ?
                """, (new_status, review_notes, voting_deadline, strategy_id))

                # Notify all members about new voting
                self._notify_members_new_vote(strategy_id)

            elif action == 'reject':
                new_status = 'rejected'
                self.db.conn.execute("""
                    UPDATE strategy_proposals
                    SET status = ?, admin_review_notes = ?
                    WHERE id = ?
                """, (new_status, review_notes, strategy_id))

                # Notify proposer
                self._notify_strategy_result(strategy_id, 'rejected')

            elif action == 'request_changes':
                new_status = 'changes_requested'
                self.db.conn.execute("""
                    UPDATE strategy_proposals
                    SET status = ?, admin_review_notes = ?
                    WHERE id = ?
                """, (new_status, review_notes, strategy_id))

                # Notify proposer
                self._notify_strategy_result(strategy_id, 'changes_requested')

            # Log admin activity
            self._log_member_activity(admin_user_id, 'admin_review', {
                'strategy_id': strategy_id,
                'action': action,
                'notes': review_notes
            })

            self.db.conn.commit()
            return True

        except Exception as e:
            logger.error(f"Error in admin review: {e}")
            return False

    def cast_vote(self, user_id: int, strategy_id: int, vote: str,
                  reasoning: str = "") -> bool:
        """Cast a vote on a strategy proposal."""
        try:
            if vote not in ['approve', 'reject', 'abstain']:
                return False

            # Check if strategy is in voting phase
            cursor = self.db.conn.execute("""
                SELECT status, voting_deadline FROM strategy_proposals WHERE id = ?
            """, (strategy_id,))

            row = cursor.fetchone()
            if not row or row[0] != 'voting':
                return False

            # Check if voting deadline has passed
            if row[1] and datetime.fromisoformat(row[1]) < datetime.now():
                return False

            # Insert or update vote
            self.db.conn.execute("""
                INSERT OR REPLACE INTO strategy_votes
                (strategy_id, user_id, vote, reasoning)
                VALUES (?, ?, ?, ?)
            """, (strategy_id, user_id, vote, reasoning))

            # Log member activity
            self._log_member_activity(user_id, 'vote', {
                'strategy_id': strategy_id,
                'vote': vote,
                'reasoning': reasoning
            })

            # Update member profile vote count
            self.db.conn.execute("""
                UPDATE member_profiles
                SET total_votes = total_votes + 1
                WHERE user_id = ?
            """, (user_id,))

            self.db.conn.commit()

            # Check if voting should be concluded
            self._check_voting_conclusion(strategy_id)

            return True

        except Exception as e:
            logger.error(f"Error casting vote: {e}")
            return False

    def _check_voting_conclusion(self, strategy_id: int):
        """Check if voting should be concluded and process results."""
        try:
            # Get voting configuration
            min_votes = int(self.club_db.get_voting_config('min_votes_required') or '3')
            approval_threshold = float(self.club_db.get_voting_config('approval_threshold') or '0.6')

            # Count votes
            cursor = self.db.conn.execute("""
                SELECT
                    COUNT(*) as total_votes,
                    SUM(CASE WHEN vote = 'approve' THEN 1 ELSE 0 END) as approve_votes,
                    SUM(CASE WHEN vote = 'reject' THEN 1 ELSE 0 END) as reject_votes
                FROM strategy_votes
                WHERE strategy_id = ?
            """, (strategy_id,))

            vote_counts = cursor.fetchone()
            total_votes, approve_votes, reject_votes = vote_counts

            # Check if minimum votes reached
            if total_votes < min_votes:
                return

            # Calculate approval percentage
            approval_rate = approve_votes / total_votes if total_votes > 0 else 0

            # Determine result
            if approval_rate >= approval_threshold:
                new_status = 'approved'
                self._activate_strategy(strategy_id)
            else:
                new_status = 'rejected'

            # Update strategy status
            self.db.conn.execute("""
                UPDATE strategy_proposals
                SET status = ?, vote_result = ?
                WHERE id = ?
            """, (new_status, json.dumps({
                'total_votes': total_votes,
                'approve_votes': approve_votes,
                'reject_votes': reject_votes,
                'approval_rate': approval_rate
            }), strategy_id))

            # Notify members of result
            self._notify_strategy_result(strategy_id, new_status)

            self.db.conn.commit()

        except Exception as e:
            logger.error(f"Error checking voting conclusion: {e}")

    def _activate_strategy(self, strategy_id: int):
        """Activate an approved strategy."""
        try:
            # Update strategy to active status
            self.db.conn.execute("""
                UPDATE strategy_proposals
                SET is_active = TRUE, activated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (strategy_id,))

            # Initialize performance tracking
            self.db.conn.execute("""
                INSERT INTO strategy_performance
                (strategy_id, date, followers_count)
                VALUES (?, DATE('now'), 0)
            """, (strategy_id,))

            logger.info(f"Strategy {strategy_id} activated")

        except Exception as e:
            logger.error(f"Error activating strategy: {e}")

    def add_strategy_discussion(self, user_id: int, strategy_id: int,
                              content: str, parent_id: Optional[int] = None) -> Optional[int]:
        """Add a discussion comment to a strategy."""
        try:
            cursor = self.db.conn.execute("""
                INSERT INTO strategy_discussions
                (strategy_id, user_id, parent_id, content)
                VALUES (?, ?, ?, ?)
            """, (strategy_id, user_id, parent_id, content))

            discussion_id = cursor.lastrowid

            # Log member activity
            self._log_member_activity(user_id, 'comment', {
                'strategy_id': strategy_id,
                'discussion_id': discussion_id,
                'is_reply': parent_id is not None
            })

            self.db.conn.commit()
            return discussion_id

        except Exception as e:
            logger.error(f"Error adding strategy discussion: {e}")
            return None

    def get_strategy_discussions(self, strategy_id: int) -> List[Dict[str, Any]]:
        """Get all discussions for a strategy."""
        try:
            cursor = self.db.conn.execute("""
                SELECT
                    sd.id, sd.user_id, sd.parent_id, sd.content,
                    sd.timestamp, sd.is_edited, sd.edit_timestamp,
                    u.username, mp.display_name
                FROM strategy_discussions sd
                JOIN users u ON sd.user_id = u.id
                LEFT JOIN member_profiles mp ON sd.user_id = mp.user_id
                WHERE sd.strategy_id = ?
                ORDER BY sd.timestamp ASC
            """, (strategy_id,))

            discussions = []
            for row in cursor.fetchall():
                discussions.append({
                    'id': row[0],
                    'user_id': row[1],
                    'parent_id': row[2],
                    'content': row[3],
                    'timestamp': row[4],
                    'is_edited': row[5],
                    'edit_timestamp': row[6],
                    'username': row[7],
                    'display_name': row[8] or row[7]
                })

            return discussions

        except Exception as e:
            logger.error(f"Error getting strategy discussions: {e}")
            return []

    def get_strategy_votes(self, strategy_id: int) -> Dict[str, Any]:
        """Get voting summary for a strategy."""
        try:
            cursor = self.db.conn.execute("""
                SELECT
                    sv.vote, sv.reasoning, sv.timestamp,
                    u.username, mp.display_name
                FROM strategy_votes sv
                JOIN users u ON sv.user_id = u.id
                LEFT JOIN member_profiles mp ON sv.user_id = mp.user_id
                WHERE sv.strategy_id = ?
                ORDER BY sv.timestamp DESC
            """, (strategy_id,))

            votes = []
            vote_counts = {'approve': 0, 'reject': 0, 'abstain': 0}

            for row in cursor.fetchall():
                vote_data = {
                    'vote': row[0],
                    'reasoning': row[1],
                    'timestamp': row[2],
                    'username': row[3],
                    'display_name': row[4] or row[3]
                }
                votes.append(vote_data)
                vote_counts[row[0]] += 1

            total_votes = sum(vote_counts.values())
            approval_rate = vote_counts['approve'] / total_votes if total_votes > 0 else 0

            return {
                'votes': votes,
                'summary': {
                    'total_votes': total_votes,
                    'approve_votes': vote_counts['approve'],
                    'reject_votes': vote_counts['reject'],
                    'abstain_votes': vote_counts['abstain'],
                    'approval_rate': approval_rate
                }
            }

        except Exception as e:
            logger.error(f"Error getting strategy votes: {e}")
            return {'votes': [], 'summary': {}}

    def _log_member_activity(self, user_id: int, activity_type: str, data: Dict[str, Any]):
        """Log member activity."""
        try:
            self.db.conn.execute("""
                INSERT INTO member_activities
                (user_id, activity_type, activity_data)
                VALUES (?, ?, ?)
            """, (user_id, activity_type, json.dumps(data)))

        except Exception as e:
            logger.error(f"Error logging member activity: {e}")

    def _notify_admins_for_review(self, strategy_id: int, title: str):
        """Notify admins of new strategy for review."""
        try:
            # Get all admin users
            cursor = self.db.conn.execute("""
                SELECT id FROM users WHERE role = 'admin'
            """)

            admin_ids = [row[0] for row in cursor.fetchall()]

            for admin_id in admin_ids:
                self.db.conn.execute("""
                    INSERT INTO club_notifications
                    (user_id, notification_type, title, content, related_id)
                    VALUES (?, 'admin_review', ?, ?, ?)
                """, (admin_id, 'New Strategy for Review',
                      f'Strategy "{title}" requires admin review', strategy_id))

        except Exception as e:
            logger.error(f"Error notifying admins: {e}")

    def _notify_members_new_vote(self, strategy_id: int):
        """Notify all members of new voting opportunity."""
        try:
            # Get strategy details
            cursor = self.db.conn.execute("""
                SELECT title FROM strategy_proposals WHERE id = ?
            """, (strategy_id,))

            row = cursor.fetchone()
            if not row:
                return

            title = row[0]

            # Get all members
            cursor = self.db.conn.execute("""
                SELECT id FROM users WHERE role IN ('admin', 'member')
            """)

            member_ids = [row[0] for row in cursor.fetchall()]

            for member_id in member_ids:
                self.db.conn.execute("""
                    INSERT INTO club_notifications
                    (user_id, notification_type, title, content, related_id)
                    VALUES (?, 'new_vote', ?, ?, ?)
                """, (member_id, 'New Strategy Vote',
                      f'Vote on strategy: "{title}"', strategy_id))

        except Exception as e:
            logger.error(f"Error notifying members: {e}")

    def _notify_strategy_result(self, strategy_id: int, result: str):
        """Notify relevant users of strategy result."""
        try:
            # Get strategy details and proposer
            cursor = self.db.conn.execute("""
                SELECT title, user_id FROM strategy_proposals WHERE id = ?
            """, (strategy_id,))

            row = cursor.fetchone()
            if not row:
                return

            title, proposer_id = row

            # Notify proposer
            self.db.conn.execute("""
                INSERT INTO club_notifications
                (user_id, notification_type, title, content, related_id)
                VALUES (?, 'strategy_result', ?, ?, ?)
            """, (proposer_id, 'Strategy Result',
                  f'Your strategy "{title}" was {result}', strategy_id))

            # If approved, notify all members
            if result == 'approved':
                cursor = self.db.conn.execute("""
                    SELECT id FROM users WHERE role IN ('admin', 'member') AND id != ?
                """, (proposer_id,))

                member_ids = [row[0] for row in cursor.fetchall()]

                for member_id in member_ids:
                    self.db.conn.execute("""
                        INSERT INTO club_notifications
                        (user_id, notification_type, title, content, related_id)
                        VALUES (?, 'strategy_approved', ?, ?, ?)
                    """, (member_id, 'Strategy Approved',
                          f'New strategy available: "{title}"', strategy_id))

        except Exception as e:
            logger.error(f"Error notifying strategy result: {e}")

    def get_pending_strategies(self) -> List[Dict[str, Any]]:
        """Get strategies pending admin review."""
        try:
            cursor = self.db.conn.execute("""
                SELECT
                    sp.id, sp.title, sp.description, sp.strategy_type,
                    sp.risk_level, sp.expected_return, sp.created_at,
                    u.username, mp.display_name
                FROM strategy_proposals sp
                JOIN users u ON sp.user_id = u.id
                LEFT JOIN member_profiles mp ON sp.user_id = mp.user_id
                WHERE sp.status = 'pending_review'
                ORDER BY sp.created_at ASC
            """)

            strategies = []
            for row in cursor.fetchall():
                strategies.append({
                    'id': row[0],
                    'title': row[1],
                    'description': row[2],
                    'strategy_type': row[3],
                    'risk_level': row[4],
                    'expected_return': row[5],
                    'created_at': row[6],
                    'proposer_username': row[7],
                    'proposer_display_name': row[8] or row[7]
                })

            return strategies

        except Exception as e:
            logger.error(f"Error getting pending strategies: {e}")
            return []

    def get_voting_strategies(self) -> List[Dict[str, Any]]:
        """Get strategies currently in voting phase."""
        try:
            cursor = self.db.conn.execute("""
                SELECT
                    sp.id, sp.title, sp.description, sp.strategy_type,
                    sp.risk_level, sp.expected_return, sp.voting_deadline,
                    u.username, mp.display_name,
                    COUNT(sv.id) as vote_count
                FROM strategy_proposals sp
                JOIN users u ON sp.user_id = u.id
                LEFT JOIN member_profiles mp ON sp.user_id = mp.user_id
                LEFT JOIN strategy_votes sv ON sp.id = sv.strategy_id
                WHERE sp.status = 'voting'
                GROUP BY sp.id
                ORDER BY sp.voting_deadline ASC
            """)

            strategies = []
            for row in cursor.fetchall():
                strategies.append({
                    'id': row[0],
                    'title': row[1],
                    'description': row[2],
                    'strategy_type': row[3],
                    'risk_level': row[4],
                    'expected_return': row[5],
                    'voting_deadline': row[6],
                    'proposer_username': row[7],
                    'proposer_display_name': row[8] or row[7],
                    'vote_count': row[9]
                })

            return strategies

        except Exception as e:
            logger.error(f"Error getting voting strategies: {e}")
            return []

    def get_approved_strategies(self) -> List[Dict[str, Any]]:
        """Get approved and active strategies."""
        try:
            cursor = self.db.conn.execute("""
                SELECT
                    sp.id, sp.title, sp.description, sp.strategy_type,
                    sp.risk_level, sp.expected_return, sp.activated_at,
                    u.username, mp.display_name,
                    COUNT(sf.id) as followers_count,
                    AVG(spr.total_return) as avg_return
                FROM strategy_proposals sp
                JOIN users u ON sp.user_id = u.id
                LEFT JOIN member_profiles mp ON sp.user_id = mp.user_id
                LEFT JOIN strategy_following sf ON sp.id = sf.strategy_id AND sf.is_active = TRUE
                LEFT JOIN strategy_performance spr ON sp.id = spr.strategy_id
                WHERE sp.status = 'approved' AND sp.is_active = TRUE
                GROUP BY sp.id
                ORDER BY followers_count DESC, avg_return DESC
            """)

            strategies = []
            for row in cursor.fetchall():
                strategies.append({
                    'id': row[0],
                    'title': row[1],
                    'description': row[2],
                    'strategy_type': row[3],
                    'risk_level': row[4],
                    'expected_return': row[5],
                    'activated_at': row[6],
                    'proposer_username': row[7],
                    'proposer_display_name': row[8] or row[7],
                    'followers_count': row[9] or 0,
                    'avg_return': row[10] or 0.0
                })

            return strategies

        except Exception as e:
            logger.error(f"Error getting approved strategies: {e}")
            return []
