#!/usr/bin/env python3
"""
Money Circle Advanced Market Data Manager
Professional-grade real-time market data integration with multiple exchange feeds,
intelligent fallback mechanisms, and comprehensive data validation.
"""

import asyncio
import json
import logging
import websockets
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import numpy as np

logger = logging.getLogger(__name__)

class DataSource(Enum):
    HTX = "htx"
    BINANCE = "binance"
    BYBIT = "bybit"
    COINGECKO = "coingecko"
    MOCK = "mock"

class DataQuality(Enum):
    EXCELLENT = "excellent"  # Real-time WebSocket data
    GOOD = "good"           # Recent REST API data
    FAIR = "fair"           # Cached data < 5 minutes old
    POOR = "poor"           # Fallback/mock data

@dataclass
class MarketTick:
    """Real-time market tick data."""
    symbol: str
    price: float
    bid: float
    ask: float
    volume_24h: float
    change_24h: float
    change_24h_percent: float
    high_24h: float
    low_24h: float
    timestamp: datetime
    source: DataSource
    quality: DataQuality
    latency_ms: float

@dataclass
class OrderBookLevel:
    """Order book price level."""
    price: float
    quantity: float

@dataclass
class OrderBook:
    """Real-time order book data."""
    symbol: str
    bids: List[OrderBookLevel]
    asks: List[OrderBookLevel]
    timestamp: datetime
    source: DataSource

@dataclass
class Trade:
    """Real-time trade data."""
    symbol: str
    price: float
    quantity: float
    side: str  # 'buy' or 'sell'
    timestamp: datetime
    trade_id: str
    source: DataSource

class AdvancedMarketDataManager:
    """Professional-grade market data manager with multi-exchange integration."""

    def __init__(self, config: Dict[str, Any]):
        self.config = config

        # Data storage
        self.market_ticks: Dict[str, MarketTick] = {}
        self.order_books: Dict[str, OrderBook] = {}
        self.recent_trades: Dict[str, List[Trade]] = {}

        # WebSocket connections
        self.websocket_connections: Dict[DataSource, Optional[websockets.WebSocketServerProtocol]] = {}
        self.connection_status: Dict[DataSource, bool] = {}

        # Data quality tracking
        self.data_freshness: Dict[str, datetime] = {}
        self.source_priorities = [DataSource.BINANCE, DataSource.HTX, DataSource.BYBIT, DataSource.COINGECKO]

        # Subscribers for real-time updates
        self.subscribers: List[Callable[[str, MarketTick], None]] = []

        # Configuration
        self.symbols = config.get('symbols', ['BTC/USDT', 'ETH/USDT', 'BNB/USDT'])
        self.update_interval = config.get('update_interval', 1.0)  # 1 second
        self.max_trade_history = config.get('max_trade_history', 100)

        # Control flags
        self.running = False
        self.health_check_interval = 30  # 30 seconds

        logger.info("[DATA] Advanced Market Data Manager initialized")

    async def start(self):
        """Start the market data manager with all data sources."""
        if self.running:
            return

        self.running = True
        logger.info("🚀 Starting advanced market data manager...")

        # Start all data source connections
        tasks = [
            self._start_binance_websocket(),
            self._start_htx_websocket(),
            self._start_bybit_websocket(),
            self._start_coingecko_polling(),
            self._start_health_monitoring(),
            self._start_data_quality_monitoring()
        ]

        await asyncio.gather(*tasks, return_exceptions=True)

    async def _start_binance_websocket(self):
        """Start Binance WebSocket connection for real-time data."""
        while self.running:
            try:
                # Binance WebSocket URL for 24hr ticker
                symbols_param = '/'.join([s.replace('/', '').lower() for s in self.symbols])
                url = f"wss://stream.binance.com:9443/ws/{symbols_param}@ticker"

                logger.info("🔄 Connecting to Binance WebSocket...")

                async with websockets.connect(url) as websocket:
                    self.websocket_connections[DataSource.BINANCE] = websocket
                    self.connection_status[DataSource.BINANCE] = True
                    logger.info("✅ Connected to Binance WebSocket")

                    async for message in websocket:
                        if not self.running:
                            break

                        try:
                            data = json.loads(message)
                            await self._process_binance_ticker(data)
                        except Exception as e:
                            logger.error(f"❌ Error processing Binance data: {e}")

            except Exception as e:
                logger.error(f"❌ Binance WebSocket error: {e}")
                self.connection_status[DataSource.BINANCE] = False
                await asyncio.sleep(5)  # Wait before reconnecting

    async def _process_binance_ticker(self, data: Dict[str, Any]):
        """Process Binance ticker data."""
        try:
            symbol = data['s']  # e.g., 'BTCUSDT'

            # Convert to standard format
            if symbol.endswith('USDT'):
                standard_symbol = f"{symbol[:-4]}/USDT"
            else:
                return  # Skip non-USDT pairs

            if standard_symbol not in self.symbols:
                return

            # Create market tick
            tick = MarketTick(
                symbol=standard_symbol,
                price=float(data['c']),  # Current price
                bid=float(data['b']),    # Best bid
                ask=float(data['a']),    # Best ask
                volume_24h=float(data['v']),  # 24h volume
                change_24h=float(data['P']),  # 24h change percent
                change_24h_percent=float(data['P']),
                high_24h=float(data['h']),    # 24h high
                low_24h=float(data['l']),     # 24h low
                timestamp=datetime.now(),
                source=DataSource.BINANCE,
                quality=DataQuality.EXCELLENT,
                latency_ms=0.0  # Real-time WebSocket
            )

            # Update storage
            self.market_ticks[standard_symbol] = tick
            self.data_freshness[standard_symbol] = datetime.now()

            # Notify subscribers
            await self._notify_subscribers(standard_symbol, tick)

        except Exception as e:
            logger.error(f"❌ Error processing Binance ticker: {e}")

    async def _start_htx_websocket(self):
        """Start HTX (Huobi) WebSocket connection with proper ping/pong handling."""
        while self.running:
            try:
                url = "wss://api-aws.huobi.pro/ws"

                logger.info("🔄 Connecting to HTX WebSocket...")

                # HTX requires proper ping/pong handling to prevent disconnections
                async with websockets.connect(
                    url,
                    ping_interval=None,  # Disable built-in ping, we'll handle HTX's custom ping
                    ping_timeout=None,
                    close_timeout=10
                ) as websocket:
                    self.websocket_connections[DataSource.HTX] = websocket
                    self.connection_status[DataSource.HTX] = True
                    logger.info("✅ Connected to HTX WebSocket")

                    # Subscribe to tickers
                    for symbol in self.symbols:
                        htx_symbol = symbol.replace('/', '').lower()
                        subscribe_msg = {
                            "sub": f"market.{htx_symbol}.detail",
                            "id": f"id_{htx_symbol}"
                        }
                        await websocket.send(json.dumps(subscribe_msg))
                        logger.debug(f"📡 Subscribed to HTX {htx_symbol}")

                    # Track connection health
                    last_ping_time = 0
                    ping_count = 0

                    async for message in websocket:
                        if not self.running:
                            break

                        try:
                            # HTX sends gzipped data
                            import gzip
                            data = json.loads(gzip.decompress(message).decode('utf-8'))

                            # Handle ping/pong first to maintain connection
                            if 'ping' in data:
                                ping_count += 1
                                ping_timestamp = data['ping']
                                last_ping_time = ping_timestamp

                                # Respond with pong immediately
                                pong_msg = {"pong": ping_timestamp}
                                await websocket.send(json.dumps(pong_msg))

                                logger.debug(f"🏓 HTX ping/pong #{ping_count}: {ping_timestamp}")
                                continue

                            # Process market data
                            await self._process_htx_data(data)

                        except gzip.BadGzipFile:
                            logger.warning("⚠️ HTX sent non-gzipped data, trying plain JSON...")
                            try:
                                data = json.loads(message)
                                if 'ping' in data:
                                    pong_msg = {"pong": data['ping']}
                                    await websocket.send(json.dumps(pong_msg))
                                    continue
                                await self._process_htx_data(data)
                            except Exception as e:
                                logger.error(f"❌ Error processing HTX plain data: {e}")
                        except json.JSONDecodeError as e:
                            logger.error(f"❌ HTX JSON decode error: {e}")
                        except Exception as e:
                            logger.error(f"❌ Error processing HTX data: {e}")

            except websockets.exceptions.ConnectionClosed as e:
                logger.warning(f"⚠️ HTX WebSocket connection closed: {e}")
                self.connection_status[DataSource.HTX] = False
            except Exception as e:
                logger.error(f"❌ HTX WebSocket error: {e}")
                self.connection_status[DataSource.HTX] = False

            # Wait before reconnecting
            if self.running:
                logger.info("🔄 Reconnecting to HTX WebSocket in 5 seconds...")
                await asyncio.sleep(5)

    async def _process_htx_data(self, data: Dict[str, Any]):
        """Process HTX market data (ping/pong handled in main loop)."""
        try:
            # Skip ping messages (handled in main loop)
            if 'ping' in data:
                return

            # Handle subscription confirmations
            if 'subbed' in data:
                logger.debug(f"✅ HTX subscription confirmed: {data.get('subbed')}")
                return

            # Handle error messages
            if 'status' in data and data['status'] == 'error':
                logger.error(f"❌ HTX subscription error: {data.get('err-msg', 'Unknown error')}")
                return

            # Process market data
            if 'ch' not in data or 'tick' not in data:
                return

            channel = data['ch']
            tick_data = data['tick']

            # Extract symbol from channel
            if 'market.' in channel and '.detail' in channel:
                htx_symbol = channel.split('.')[1]

                # Convert to standard format
                if htx_symbol.endswith('usdt'):
                    base = htx_symbol[:-4].upper()
                    standard_symbol = f"{base}/USDT"
                else:
                    return

                if standard_symbol not in self.symbols:
                    return

                # Create market tick
                tick = MarketTick(
                    symbol=standard_symbol,
                    price=float(tick_data['close']),
                    bid=float(tick_data.get('bid', tick_data['close'])),
                    ask=float(tick_data.get('ask', tick_data['close'])),
                    volume_24h=float(tick_data['vol']),
                    change_24h=float(tick_data['close']) - float(tick_data['open']),
                    change_24h_percent=((float(tick_data['close']) - float(tick_data['open'])) / float(tick_data['open'])) * 100,
                    high_24h=float(tick_data['high']),
                    low_24h=float(tick_data['low']),
                    timestamp=datetime.now(),
                    source=DataSource.HTX,
                    quality=DataQuality.EXCELLENT,
                    latency_ms=0.0
                )

                # Update storage
                self.market_ticks[standard_symbol] = tick
                self.data_freshness[standard_symbol] = datetime.now()

                # Notify subscribers
                await self._notify_subscribers(standard_symbol, tick)

        except Exception as e:
            logger.error(f"❌ Error processing HTX data: {e}")

    async def _start_bybit_websocket(self):
        """Start Bybit WebSocket connection with improved error handling."""
        while self.running:
            try:
                # Use spot WebSocket for USDT pairs (most common case)
                url = "wss://stream.bybit.com/v5/public/spot"

                logger.info("🔄 Connecting to Bybit Spot WebSocket...")

                async with websockets.connect(url, ping_interval=20, ping_timeout=10) as websocket:
                    self.websocket_connections[DataSource.BYBIT] = websocket
                    self.connection_status[DataSource.BYBIT] = True
                    logger.info("✅ Connected to Bybit Spot WebSocket")

                    # Subscribe to tickers
                    symbols_list = [s.replace('/', '') for s in self.symbols]
                    subscribe_msg = {
                        "op": "subscribe",
                        "args": [f"tickers.{symbol}" for symbol in symbols_list]
                    }
                    await websocket.send(json.dumps(subscribe_msg))
                    logger.info(f"📡 Subscribed to Bybit tickers: {symbols_list}")

                    async for message in websocket:
                        if not self.running:
                            break

                        try:
                            data = json.loads(message)

                            # Handle subscription confirmation
                            if data.get('op') == 'subscribe':
                                if data.get('success'):
                                    logger.info(f"✅ Bybit subscription confirmed: {data.get('ret_msg', 'OK')}")
                                else:
                                    logger.error(f"❌ Bybit subscription failed: {data.get('ret_msg', 'Unknown error')}")
                                continue

                            # Process ticker data
                            await self._process_bybit_data(data)

                        except json.JSONDecodeError as e:
                            logger.error(f"❌ Invalid JSON from Bybit: {e}")
                        except Exception as e:
                            logger.error(f"❌ Error processing Bybit message: {e}")

            except websockets.exceptions.ConnectionClosed as e:
                logger.warning(f"⚠️ Bybit WebSocket connection closed: {e}")
                self.connection_status[DataSource.BYBIT] = False
            except websockets.exceptions.InvalidURI as e:
                logger.error(f"❌ Invalid Bybit WebSocket URI: {e}")
                self.connection_status[DataSource.BYBIT] = False
                await asyncio.sleep(30)  # Longer wait for config issues
            except Exception as e:
                logger.error(f"❌ Bybit WebSocket error: {e}")
                self.connection_status[DataSource.BYBIT] = False

            # Wait before reconnecting
            if self.running:
                logger.info("🔄 Reconnecting to Bybit WebSocket in 5 seconds...")
                await asyncio.sleep(5)

    async def _process_bybit_data(self, data: Dict[str, Any]):
        """Process Bybit market data with proper field handling for different market types."""
        try:
            if data.get('topic', '').startswith('tickers.') and 'data' in data:
                tick_data = data['data']
                bybit_symbol = tick_data['symbol']

                # Convert to standard format
                if bybit_symbol.endswith('USDT'):
                    base = bybit_symbol[:-4]
                    standard_symbol = f"{base}/USDT"
                else:
                    return

                if standard_symbol not in self.symbols:
                    return

                # Extract price data with defensive programming
                last_price = float(tick_data.get('lastPrice', 0))
                if last_price <= 0:
                    logger.warning(f"⚠️ Invalid lastPrice for {standard_symbol}: {last_price}")
                    return

                # Handle bid/ask prices based on market type
                # Spot markets don't have bid1Price/ask1Price, only derivatives do
                bid_price = None
                ask_price = None

                # Try different field names for bid/ask based on Bybit API variations
                if 'bid1Price' in tick_data and tick_data['bid1Price']:
                    # Linear/Inverse derivatives format
                    bid_price = float(tick_data['bid1Price'])
                    ask_price = float(tick_data['ask1Price'])
                elif 'bidPrice' in tick_data and tick_data['bidPrice']:
                    # Options format
                    bid_price = float(tick_data['bidPrice'])
                    ask_price = float(tick_data['askPrice'])
                else:
                    # Spot format - no bid/ask in ticker, estimate from last price
                    # Use a small spread estimation (0.1% typical for major pairs)
                    spread_percent = 0.001  # 0.1%
                    spread = last_price * spread_percent
                    bid_price = last_price - spread / 2
                    ask_price = last_price + spread / 2
                    logger.debug(f"📊 Estimated bid/ask for Spot {standard_symbol}: bid={bid_price:.6f}, ask={ask_price:.6f}")

                # Extract volume and price change data
                volume_24h = float(tick_data.get('volume24h', 0))
                price_24h_pcnt = float(tick_data.get('price24hPcnt', 0))
                high_24h = float(tick_data.get('highPrice24h', last_price))
                low_24h = float(tick_data.get('lowPrice24h', last_price))

                # Calculate absolute change from percentage
                change_24h = price_24h_pcnt * last_price

                # Create market tick
                tick = MarketTick(
                    symbol=standard_symbol,
                    price=last_price,
                    bid=bid_price,
                    ask=ask_price,
                    volume_24h=volume_24h,
                    change_24h_percent=price_24h_pcnt * 100,  # Convert to percentage
                    change_24h=change_24h,
                    high_24h=high_24h,
                    low_24h=low_24h,
                    timestamp=datetime.now(),
                    source=DataSource.BYBIT,
                    quality=DataQuality.EXCELLENT,
                    latency_ms=0.0
                )

                # Update storage
                self.market_ticks[standard_symbol] = tick
                self.data_freshness[standard_symbol] = datetime.now()

                # Notify subscribers
                await self._notify_subscribers(standard_symbol, tick)

                logger.debug(f"[DATA] Processed Bybit data for {standard_symbol}: price={last_price}, bid={bid_price}, ask={ask_price}")

        except KeyError as e:
            logger.error(f"[ERROR] Missing field in Bybit data for {tick_data.get('symbol', 'unknown')}: {e}")
            logger.debug(f"[DEBUG] Available fields: {list(tick_data.keys())}")
        except ValueError as e:
            logger.error(f"[ERROR] Invalid numeric value in Bybit data: {e}")
        except Exception as e:
            logger.error(f"[ERROR] Unexpected error processing Bybit data: {e}")
            logger.debug(f"[DEBUG] Raw data: {data}")

    async def _start_coingecko_polling(self):
        """Start CoinGecko API polling as fallback."""
        while self.running:
            try:
                await self._fetch_coingecko_data()
                await asyncio.sleep(60)  # Poll every minute
            except Exception as e:
                logger.error(f"❌ CoinGecko polling error: {e}")
                await asyncio.sleep(30)

    async def _fetch_coingecko_data(self):
        """Fetch data from CoinGecko API."""
        try:
            # Map symbols to CoinGecko IDs
            symbol_map = {
                'BTC/USDT': 'bitcoin',
                'ETH/USDT': 'ethereum',
                'BNB/USDT': 'binancecoin'
            }

            ids = ','.join([symbol_map.get(s) for s in self.symbols if s in symbol_map])
            url = f"https://api.coingecko.com/api/v3/simple/price?ids={ids}&vs_currencies=usd&include_24hr_change=true&include_24hr_vol=true"

            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        await self._process_coingecko_data(data, symbol_map)
                    else:
                        logger.warning(f"CoinGecko API error: {response.status}")

        except Exception as e:
            logger.error(f"❌ Error fetching CoinGecko data: {e}")

    async def _process_coingecko_data(self, data: Dict[str, Any], symbol_map: Dict[str, str]):
        """Process CoinGecko API data."""
        try:
            for symbol, coin_id in symbol_map.items():
                if coin_id in data and symbol in self.symbols:
                    coin_data = data[coin_id]

                    # Only use CoinGecko data if we don't have fresher data
                    if symbol in self.data_freshness:
                        time_since_last = datetime.now() - self.data_freshness[symbol]
                        if time_since_last.total_seconds() < 300:  # 5 minutes
                            continue

                    price = float(coin_data['usd'])
                    change_24h_percent = float(coin_data.get('usd_24h_change', 0))

                    tick = MarketTick(
                        symbol=symbol,
                        price=price,
                        bid=price * 0.999,  # Approximate bid
                        ask=price * 1.001,  # Approximate ask
                        volume_24h=float(coin_data.get('usd_24h_vol', 0)),
                        change_24h_percent=change_24h_percent,
                        change_24h=price * (change_24h_percent / 100),
                        high_24h=price * 1.05,  # Approximate
                        low_24h=price * 0.95,   # Approximate
                        timestamp=datetime.now(),
                        source=DataSource.COINGECKO,
                        quality=DataQuality.GOOD,
                        latency_ms=1000.0  # REST API latency
                    )

                    # Only update if we don't have better data
                    if symbol not in self.market_ticks or self.market_ticks[symbol].quality.value in ['fair', 'poor']:
                        self.market_ticks[symbol] = tick
                        self.data_freshness[symbol] = datetime.now()
                        await self._notify_subscribers(symbol, tick)

        except Exception as e:
            logger.error(f"❌ Error processing CoinGecko data: {e}")

    async def _start_health_monitoring(self):
        """Monitor connection health and data quality."""
        while self.running:
            try:
                await self._check_connection_health()
                await self._check_data_freshness()
                await asyncio.sleep(self.health_check_interval)
            except Exception as e:
                logger.error(f"❌ Health monitoring error: {e}")
                await asyncio.sleep(10)

    async def _check_connection_health(self):
        """Check the health of all connections."""
        for source, connected in self.connection_status.items():
            if not connected:
                logger.warning(f"⚠️ {source.value} connection is down")

    async def _check_data_freshness(self):
        """Check data freshness and quality."""
        current_time = datetime.now()

        for symbol in self.symbols:
            if symbol in self.data_freshness:
                age = current_time - self.data_freshness[symbol]

                if age.total_seconds() > 300:  # 5 minutes
                    logger.warning(f"⚠️ Stale data for {symbol}: {age.total_seconds():.0f}s old")

                    # Provide mock data as last resort
                    if symbol not in self.market_ticks or age.total_seconds() > 600:  # 10 minutes
                        await self._provide_mock_data(symbol)

    async def _provide_mock_data(self, symbol: str):
        """Provide mock data when all sources fail."""
        try:
            # Base prices for mock data
            base_prices = {
                'BTC/USDT': 97000.0,
                'ETH/USDT': 3500.0,
                'BNB/USDT': 650.0
            }

            base_price = base_prices.get(symbol, 1000.0)

            # Add some realistic variation
            import random
            variation = random.uniform(-0.02, 0.02)  # ±2%
            price = base_price * (1 + variation)

            tick = MarketTick(
                symbol=symbol,
                price=price,
                bid=price * 0.999,
                ask=price * 1.001,
                volume_24h=1000000.0,
                change_24h_percent=variation * 100,
                change_24h=price * variation,
                high_24h=price * 1.03,
                low_24h=price * 0.97,
                timestamp=datetime.now(),
                source=DataSource.MOCK,
                quality=DataQuality.POOR,
                latency_ms=0.0
            )

            self.market_ticks[symbol] = tick
            self.data_freshness[symbol] = datetime.now()
            await self._notify_subscribers(symbol, tick)

            logger.warning(f"⚠️ Using mock data for {symbol}")

        except Exception as e:
            logger.error(f"❌ Error providing mock data: {e}")

    async def _start_data_quality_monitoring(self):
        """Monitor and improve data quality."""
        while self.running:
            try:
                await self._calculate_data_quality_metrics()
                await asyncio.sleep(60)  # Check every minute
            except Exception as e:
                logger.error(f"❌ Data quality monitoring error: {e}")
                await asyncio.sleep(30)

    async def _calculate_data_quality_metrics(self):
        """Calculate data quality metrics."""
        try:
            total_symbols = len(self.symbols)
            excellent_count = sum(1 for tick in self.market_ticks.values() if tick.quality == DataQuality.EXCELLENT)
            good_count = sum(1 for tick in self.market_ticks.values() if tick.quality == DataQuality.GOOD)

            quality_score = (excellent_count * 100 + good_count * 75) / (total_symbols * 100) if total_symbols > 0 else 0

            logger.info(f"📊 Data quality score: {quality_score:.1f}% ({excellent_count} excellent, {good_count} good)")

        except Exception as e:
            logger.error(f"❌ Error calculating data quality: {e}")

    async def subscribe(self, callback: Callable[[str, MarketTick], None]):
        """Subscribe to real-time market data updates."""
        self.subscribers.append(callback)

    async def _notify_subscribers(self, symbol: str, tick: MarketTick):
        """Notify all subscribers of market data updates."""
        for callback in self.subscribers:
            try:
                await callback(symbol, tick)
            except Exception as e:
                logger.error(f"❌ Error notifying subscriber: {e}")

    def get_market_tick(self, symbol: str) -> Optional[MarketTick]:
        """Get the latest market tick for a symbol."""
        return self.market_ticks.get(symbol)

    def get_all_market_ticks(self) -> Dict[str, MarketTick]:
        """Get all current market ticks."""
        return self.market_ticks.copy()

    def get_connection_status(self) -> Dict[DataSource, bool]:
        """Get the status of all connections."""
        return self.connection_status.copy()

    async def stop(self):
        """Stop the market data manager."""
        self.running = False

        # Close WebSocket connections
        for source, websocket in self.websocket_connections.items():
            if websocket:
                try:
                    await websocket.close()
                except:
                    pass

        logger.info("🛑 Advanced Market Data Manager stopped")
