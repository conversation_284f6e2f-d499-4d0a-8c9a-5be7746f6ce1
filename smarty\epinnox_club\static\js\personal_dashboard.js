// Money Circle Personal Dashboard JavaScript

// Global variables
let websocket = null;
let portfolioData = {};

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
    connectWebSocket();
    setupEventListeners();
});

function initializeDashboard() {
    console.log('🚀 Initializing Money Circle Personal Dashboard');
    
    // Load initial portfolio data
    loadPortfolioData();
    
    // Setup auto-refresh
    setInterval(refreshDashboard, 30000); // Refresh every 30 seconds
}

function connectWebSocket() {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws`;
    
    try {
        websocket = new WebSocket(wsUrl);
        
        websocket.onopen = function(event) {
            console.log('📡 WebSocket connected');
        };
        
        websocket.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            } catch (error) {
                console.error('Error parsing WebSocket message:', error);
            }
        };
        
        websocket.onclose = function(event) {
            console.log('📡 WebSocket disconnected');
            // Attempt to reconnect after 5 seconds
            setTimeout(connectWebSocket, 5000);
        };
        
        websocket.onerror = function(error) {
            console.error('WebSocket error:', error);
        };
        
    } catch (error) {
        console.error('Failed to connect WebSocket:', error);
    }
}

function handleWebSocketMessage(data) {
    console.log('📨 WebSocket message received:', data);
    
    // Update portfolio data if received
    if (data.portfolio) {
        portfolioData = data.portfolio;
        updatePortfolioDisplay();
    }
    
    // Handle real-time price updates
    if (data.prices) {
        updatePriceDisplays(data.prices);
    }
    
    // Handle position updates
    if (data.positions) {
        updatePositionsTable(data.positions);
    }
}

async function loadPortfolioData() {
    try {
        const response = await fetch('/api/portfolio');
        if (response.ok) {
            portfolioData = await response.json();
            updatePortfolioDisplay();
        } else {
            console.error('Failed to load portfolio data');
        }
    } catch (error) {
        console.error('Error loading portfolio data:', error);
    }
}

function updatePortfolioDisplay() {
    // Update portfolio stats
    updateElement('total-balance', `$${portfolioData.total_balance_usd?.toFixed(2) || '0.00'}`);
    updateElement('daily-pnl', `$${portfolioData.performance?.daily_pnl?.toFixed(2) || '0.00'}`);
    updateElement('win-rate', `${portfolioData.performance?.win_rate?.toFixed(1) || '0.0'}%`);
    updateElement('total-trades', portfolioData.performance?.total_trades || '0');
    
    // Update exchange cards
    updateExchangeCards();
    
    // Update positions table
    updatePositionsTable(portfolioData.positions || []);
    
    // Update trades table
    updateTradesTable(portfolioData.recent_trades || []);
}

function updateElement(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = value;
    }
}

function updateExchangeCards() {
    // This would update the exchange connection status and balances
    console.log('Updating exchange cards...');
}

function updatePositionsTable(positions) {
    console.log('Updating positions table with', positions.length, 'positions');
    // Implementation would update the positions table
}

function updateTradesTable(trades) {
    console.log('Updating trades table with', trades.length, 'trades');
    // Implementation would update the trades table
}

function updatePriceDisplays(prices) {
    // Update any price displays on the dashboard
    console.log('Updating price displays:', prices);
}

function setupEventListeners() {
    // Add exchange button
    const addExchangeBtn = document.querySelector('.add-exchange-btn');
    if (addExchangeBtn) {
        addExchangeBtn.addEventListener('click', showAddExchangeModal);
    }
    
    // Add exchange form
    const addExchangeForm = document.getElementById('addExchangeForm');
    if (addExchangeForm) {
        addExchangeForm.addEventListener('submit', handleAddExchange);
    }
    
    // Modal close events
    window.addEventListener('click', function(event) {
        const modal = document.getElementById('addExchangeModal');
        if (event.target === modal) {
            hideAddExchangeModal();
        }
    });
}

function showAddExchangeModal() {
    const modal = document.getElementById('addExchangeModal');
    if (modal) {
        modal.style.display = 'block';
    }
}

function hideAddExchangeModal() {
    const modal = document.getElementById('addExchangeModal');
    if (modal) {
        modal.style.display = 'none';
        // Reset form
        const form = document.getElementById('addExchangeForm');
        if (form) {
            form.reset();
        }
    }
}

async function handleAddExchange(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const exchangeData = {
        exchange: formData.get('exchange'),
        api_key: formData.get('api_key'),
        secret_key: formData.get('secret_key'),
        passphrase: formData.get('passphrase')
    };
    
    try {
        const response = await fetch('/api/exchange/add', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(exchangeData)
        });
        
        if (response.ok) {
            console.log('✅ Exchange account added successfully');
            hideAddExchangeModal();
            refreshDashboard();
            showNotification('Exchange account added successfully!', 'success');
        } else {
            const error = await response.json();
            console.error('Failed to add exchange account:', error);
            showNotification('Failed to add exchange account: ' + error.message, 'error');
        }
    } catch (error) {
        console.error('Error adding exchange account:', error);
        showNotification('Error adding exchange account', 'error');
    }
}

async function removeExchange(exchangeId) {
    if (!confirm('Are you sure you want to remove this exchange account?')) {
        return;
    }
    
    try {
        const response = await fetch(`/api/exchange/${exchangeId}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            console.log('✅ Exchange account removed successfully');
            refreshDashboard();
            showNotification('Exchange account removed successfully!', 'success');
        } else {
            const error = await response.json();
            console.error('Failed to remove exchange account:', error);
            showNotification('Failed to remove exchange account: ' + error.message, 'error');
        }
    } catch (error) {
        console.error('Error removing exchange account:', error);
        showNotification('Error removing exchange account', 'error');
    }
}

async function refreshExchange(exchangeName) {
    console.log(`🔄 Refreshing ${exchangeName} data...`);
    
    try {
        const response = await fetch(`/api/balance/${exchangeName}`);
        if (response.ok) {
            const balanceData = await response.json();
            console.log('✅ Exchange data refreshed:', balanceData);
            // Update the specific exchange card
            showNotification(`${exchangeName} data refreshed!`, 'success');
        } else {
            console.error('Failed to refresh exchange data');
            showNotification('Failed to refresh exchange data', 'error');
        }
    } catch (error) {
        console.error('Error refreshing exchange data:', error);
        showNotification('Error refreshing exchange data', 'error');
    }
}

async function closePosition(symbol) {
    if (!confirm(`Are you sure you want to close your ${symbol} position?`)) {
        return;
    }
    
    console.log(`🔄 Closing position for ${symbol}...`);
    // Implementation would close the position
    showNotification(`Position close request sent for ${symbol}`, 'info');
}

function refreshDashboard() {
    console.log('🔄 Refreshing dashboard data...');
    loadPortfolioData();
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
    `;
    
    // Set background color based on type
    switch (type) {
        case 'success':
            notification.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';
            break;
        case 'error':
            notification.style.background = 'linear-gradient(135deg, #f44336, #da190b)';
            break;
        case 'warning':
            notification.style.background = 'linear-gradient(135deg, #ff9800, #f57c00)';
            break;
        default:
            notification.style.background = 'linear-gradient(135deg, #2196F3, #1976D2)';
    }
    
    // Add to page
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 5 seconds
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 5000);
}

// Utility functions
function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

function formatPercentage(value) {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
}

function formatNumber(value, decimals = 2) {
    return value.toLocaleString('en-US', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    });
}
