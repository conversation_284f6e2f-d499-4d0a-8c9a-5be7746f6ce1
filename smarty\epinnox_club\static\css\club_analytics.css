/* Money Circle Club Analytics Styles */

.analytics-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #0f1419 0%, #1a1f2e 100%);
    color: #e2e8f0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header Styles */
.analytics-header {
    background: rgba(15, 20, 25, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.analytics-branding h1 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.analytics-branding p {
    margin: 0.25rem 0 0 0;
    color: #94a3b8;
    font-size: 0.9rem;
}

.analytics-nav {
    display: flex;
    gap: 1.5rem;
}

.analytics-nav .nav-link {
    color: #94a3b8;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    font-weight: 500;
}

.analytics-nav .nav-link:hover {
    color: #8b5cf6;
    background: rgba(139, 92, 246, 0.1);
}

.analytics-nav .nav-link.active {
    color: #8b5cf6;
    background: rgba(139, 92, 246, 0.2);
}

/* Analytics Controls */
.analytics-controls {
    background: rgba(30, 41, 59, 0.5);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1.5rem 0;
}

.controls-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.time-range-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.time-range-selector label {
    color: #94a3b8;
    font-weight: 500;
}

.time-range-selector select {
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    color: #e2e8f0;
    font-size: 0.875rem;
}

.analytics-tabs {
    display: flex;
    gap: 0.5rem;
}

.tab-btn {
    padding: 0.75rem 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    color: #94a3b8;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.tab-btn.active,
.tab-btn:hover {
    background: rgba(139, 92, 246, 0.2);
    border-color: #8b5cf6;
    color: #8b5cf6;
}

.export-controls {
    display: flex;
    gap: 0.5rem;
}

.export-btn {
    padding: 0.5rem 1rem;
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    border: none;
    border-radius: 0.5rem;
    color: white;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.export-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

/* Analytics Tabs */
.analytics-tab {
    display: none;
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

.analytics-tab.active {
    display: block;
}

/* Overview Tab */
.overview-grid {
    display: grid;
    gap: 2rem;
}

.metrics-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.metric-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border-color: rgba(139, 92, 246, 0.3);
}

.metric-icon {
    font-size: 2.5rem;
    width: 4rem;
    height: 4rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    border-radius: 1rem;
}

.metric-content {
    flex: 1;
}

.metric-value {
    font-size: 1.75rem;
    font-weight: 700;
    color: #f1f5f9;
    margin: 0 0 0.25rem 0;
}

.metric-label {
    font-size: 0.875rem;
    color: #94a3b8;
    margin: 0 0 0.5rem 0;
}

.metric-change {
    font-size: 0.875rem;
    font-weight: 600;
}

.metric-change.positive {
    color: #22c55e;
}

.metric-change.negative {
    color: #ef4444;
}

/* Chart Container */
.chart-container {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
    padding: 1.5rem;
    margin: 1rem 0;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.chart-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #f1f5f9;
}

.chart-controls {
    display: flex;
    gap: 0.5rem;
}

.chart-btn {
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    color: #94a3b8;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.875rem;
}

.chart-btn.active,
.chart-btn:hover {
    background: rgba(139, 92, 246, 0.2);
    border-color: #8b5cf6;
    color: #8b5cf6;
}

/* Quick Stats Grid */
.quick-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.75rem;
}

.stat-label {
    font-size: 0.875rem;
    color: #64748b;
    margin-bottom: 0.5rem;
}

.stat-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #f1f5f9;
}

/* Performance Tab */
.performance-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
}

.performance-metrics h3 {
    margin: 0 0 1rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #f1f5f9;
}

.metrics-table {
    width: 100%;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.75rem;
    overflow: hidden;
}

.metrics-table tr {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.metrics-table tr:last-child {
    border-bottom: none;
}

.metrics-table td {
    padding: 1rem;
    color: #e2e8f0;
}

.metrics-table td:first-child {
    color: #94a3b8;
    font-weight: 500;
}

.metric-value.positive {
    color: #22c55e;
}

.metric-value.negative {
    color: #ef4444;
}

.performance-charts {
    display: grid;
    gap: 1.5rem;
}

/* Portfolio Tab */
.portfolio-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 2rem;
}

.portfolio-allocation,
.portfolio-performance,
.portfolio-risk {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
    padding: 1.5rem;
}

.portfolio-allocation h3,
.portfolio-performance h3,
.portfolio-risk h3 {
    margin: 0 0 1rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #f1f5f9;
}

.asset-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.asset-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
}

.asset-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.asset-name {
    font-weight: 600;
    color: #f1f5f9;
}

.asset-allocation {
    font-size: 0.875rem;
    color: #94a3b8;
}

.asset-metrics {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
}

.asset-value {
    font-weight: 600;
    color: #f1f5f9;
}

.asset-return.positive {
    color: #22c55e;
}

.asset-return.negative {
    color: #ef4444;
}

.risk-indicators {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.risk-indicator {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
}

.risk-label {
    color: #94a3b8;
    font-weight: 500;
}

.risk-value {
    font-weight: 600;
    color: #f1f5f9;
}

.risk-value.negative {
    color: #ef4444;
}

/* Strategies Tab */
.strategies-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

.strategy-performance {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
    padding: 1.5rem;
}

.strategy-performance h3 {
    margin: 0 0 1rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #f1f5f9;
}

.strategy-table {
    width: 100%;
    border-collapse: collapse;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.75rem;
    overflow: hidden;
}

.strategy-table th {
    background: rgba(139, 92, 246, 0.2);
    color: #8b5cf6;
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    font-size: 0.875rem;
}

.strategy-table td {
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: #e2e8f0;
}

.strategy-table tr:last-child td {
    border-bottom: none;
}

.strategy-name {
    font-weight: 600;
    color: #f1f5f9;
}

.strategy-type {
    color: #94a3b8;
    font-size: 0.875rem;
}

.return-value.positive {
    color: #22c55e;
    font-weight: 600;
}

.return-value.negative {
    color: #ef4444;
    font-weight: 600;
}

.win-rate,
.sharpe-ratio,
.trades-count,
.followers-count {
    font-weight: 500;
}

/* Members Tab */
.members-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

.member-performance {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
    padding: 1.5rem;
}

.member-performance h3 {
    margin: 0 0 1rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #f1f5f9;
}

.member-table {
    width: 100%;
    border-collapse: collapse;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.75rem;
    overflow: hidden;
}

.member-table th {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    font-size: 0.875rem;
}

.member-table td {
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: #e2e8f0;
}

.member-table tr:last-child td {
    border-bottom: none;
}

.member-name {
    font-weight: 600;
    color: #f1f5f9;
}

.portfolio-value {
    font-weight: 600;
    color: #22c55e;
}

.member-charts {
    display: grid;
    gap: 1.5rem;
}

/* Risk Analysis Tab */
.risk-grid {
    display: grid;
    gap: 2rem;
}

.risk-overview {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
    padding: 1.5rem;
}

.risk-overview h3 {
    margin: 0 0 1rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #f1f5f9;
}

.risk-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.risk-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0.75rem;
    padding: 1.5rem;
    text-align: center;
}

.risk-card-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.risk-icon {
    font-size: 1.5rem;
}

.risk-title {
    font-weight: 600;
    color: #94a3b8;
}

.risk-card-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #f1f5f9;
    margin-bottom: 0.5rem;
}

.risk-card-status {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.risk-card-status.good {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.risk-card-status.moderate {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
}

.risk-card-status.low {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.risk-charts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

/* Empty Data */
.empty-data {
    text-align: center;
    padding: 2rem;
    color: #64748b;
    font-style: italic;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .portfolio-grid {
        grid-template-columns: 1fr 1fr;
    }
    
    .strategies-grid,
    .members-grid {
        grid-template-columns: 1fr;
    }
    
    .risk-charts {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        text-align: center;
    }
    
    .analytics-nav {
        order: -1;
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .controls-content {
        flex-direction: column;
        align-items: stretch;
    }
    
    .analytics-tabs {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .metrics-row {
        grid-template-columns: 1fr;
    }
    
    .performance-grid,
    .portfolio-grid {
        grid-template-columns: 1fr;
    }
    
    .quick-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .risk-cards {
        grid-template-columns: repeat(2, 1fr);
    }
}
