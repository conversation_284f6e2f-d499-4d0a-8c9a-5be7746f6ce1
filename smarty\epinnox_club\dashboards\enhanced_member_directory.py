#!/usr/bin/env python3
"""
Money Circle Enhanced Member Directory
Comprehensive member profile system with trading statistics, leaderboards, and social features.
"""

import logging
import json
from typing import Dict, List, Any, Optional
from aiohttp import web
from datetime import datetime, timedelta
from auth.decorators import get_current_user
from database.models import DatabaseManager
from database.club_models import ClubDatabaseManager
from club.social_trading import SocialTrading
from club.analytics import ClubAnalytics

logger = logging.getLogger(__name__)

class EnhancedMemberDirectory:
    """Professional member directory with advanced features."""

    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.club_db = ClubDatabaseManager(db_manager)
        self.social_trading = SocialTrading(db_manager)
        self.analytics = ClubAnalytics(db_manager)

    async def serve_member_directory(self, request: web.Request) -> web.Response:
        """Serve the enhanced member directory."""
        user = get_current_user(request)
        if not user:
            return web.Response(status=302, headers={'Location': '/login'})

        # Get member data
        members = self._get_all_members()
        leaderboards = self._get_leaderboards()
        member_stats = self._get_member_stats()
        user_connections = self._get_user_connections(user['user_id'])
        achievements = self._get_member_achievements()

        html_content = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Money Circle - Member Directory</title>
            <link rel="stylesheet" href="/static/css/design_system.css">
            <link rel="stylesheet" href="/static/css/unified_header.css">
            <link rel="stylesheet" href="/static/css/unified_footer.css">
            <link rel="stylesheet" href="/static/css/dashboard.css">
            <link rel="stylesheet" href="/static/css/club.css">
            <link rel="stylesheet" href="/static/css/member_directory.css">
            <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
            <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        </head>
        <body>
            <div class="directory-container member-directory">
                <!-- Unified Navigation Header -->
                <header class="unified-header">
                    <div class="header-content">
                        <div class="header-branding">
                            <div class="logo">MC</div>
                            <div class="brand-text">
                                <h1>👥 Member Directory</h1>
                                <p>Connect with fellow traders and investment professionals</p>
                            </div>
                        </div>
                        <nav class="header-nav">
                            <a href="/dashboard" class="nav-link">Personal Dashboard</a>
                            <a href="/club" class="nav-link">Club Dashboard</a>
                            <a href="/club/strategies" class="nav-link">Strategy Marketplace</a>
                            <a href="/club/members" class="nav-link active">Member Directory</a>
                            <a href="/club/analytics" class="nav-link">Club Analytics</a>
                        </nav>
                        <div class="header-user-info">
                            <div class="user-avatar">{user.get('username', 'M')[0].upper()}</div>
                            <div class="user-details">
                                <div class="user-welcome">Welcome, {user.get('username', 'Member')}</div>
                                <div class="user-role">Investment Club Member</div>
                            </div>
                            <a href="/logout" class="logout-btn">Logout</a>
                        </div>
                    </div>
                </header>

                <!-- Breadcrumb Navigation -->
                <div class="header-breadcrumb">
                    <div class="breadcrumb-content">
                        <a href="/club" class="breadcrumb-item">Club Dashboard</a>
                        <span class="breadcrumb-separator">›</span>
                        <span class="breadcrumb-item active">Member Directory</span>
                    </div>
                </div>

                <!-- Member Stats Overview -->
                <section class="member-stats-overview">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">👥</div>
                            <div class="stat-content">
                                <div class="stat-value">{member_stats['total_members']}</div>
                                <div class="stat-label">Total Members</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">🏆</div>
                            <div class="stat-content">
                                <div class="stat-value">{member_stats['active_traders']}</div>
                                <div class="stat-label">Active Traders</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">📈</div>
                            <div class="stat-content">
                                <div class="stat-value">{member_stats['avg_performance']:.1f}%</div>
                                <div class="stat-label">Avg Performance</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">🤝</div>
                            <div class="stat-content">
                                <div class="stat-value">{member_stats['total_connections']}</div>
                                <div class="stat-label">Total Connections</div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Search and Filter Controls -->
                <section class="directory-controls">
                    <div class="search-section">
                        <div class="search-bar">
                            <input type="text" id="memberSearch" placeholder="Search members by name, specialization, or location..." />
                            <button class="search-btn" onclick="searchMembers()">🔍</button>
                        </div>
                        <div class="view-toggles">
                            <button class="view-toggle active" data-view="grid">Grid View</button>
                            <button class="view-toggle" data-view="list">List View</button>
                            <button class="view-toggle" data-view="leaderboard">Leaderboards</button>
                        </div>
                    </div>

                    <div class="filter-controls">
                        <div class="filter-group">
                            <label>Experience Level</label>
                            <select id="experienceFilter" onchange="applyMemberFilters()">
                                <option value="">All Levels</option>
                                <option value="beginner">Beginner</option>
                                <option value="intermediate">Intermediate</option>
                                <option value="advanced">Advanced</option>
                                <option value="expert">Expert</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>Specialization</label>
                            <select id="specializationFilter" onchange="applyMemberFilters()">
                                <option value="">All Specializations</option>
                                <option value="day_trading">Day Trading</option>
                                <option value="swing_trading">Swing Trading</option>
                                <option value="long_term">Long Term</option>
                                <option value="crypto">Cryptocurrency</option>
                                <option value="forex">Forex</option>
                                <option value="options">Options</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>Performance</label>
                            <select id="performanceFilter" onchange="applyMemberFilters()">
                                <option value="">All Performance</option>
                                <option value="top_10">Top 10%</option>
                                <option value="top_25">Top 25%</option>
                                <option value="positive">Positive Returns</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>Sort By</label>
                            <select id="sortFilter" onchange="applyMemberFilters()">
                                <option value="performance">Performance</option>
                                <option value="experience">Experience</option>
                                <option value="connections">Connections</option>
                                <option value="recent">Recently Joined</option>
                                <option value="alphabetical">Alphabetical</option>
                            </select>
                        </div>
                    </div>
                </section>

                <!-- Leaderboards Section -->
                <section class="leaderboards-section" id="leaderboardsSection" style="display: none;">
                    <div class="leaderboards-grid">
                        <div class="leaderboard-card">
                            <h3>🏆 Top Performers</h3>
                            <div class="leaderboard-list">
                                {self._render_leaderboard(leaderboards.get('performance', []), 'performance')}
                            </div>
                        </div>
                        <div class="leaderboard-card">
                            <h3>🎯 Most Consistent</h3>
                            <div class="leaderboard-list">
                                {self._render_leaderboard(leaderboards.get('consistency', []), 'consistency')}
                            </div>
                        </div>
                        <div class="leaderboard-card">
                            <h3>🛡️ Risk Management</h3>
                            <div class="leaderboard-list">
                                {self._render_leaderboard(leaderboards.get('risk_management', []), 'risk')}
                            </div>
                        </div>
                        <div class="leaderboard-card">
                            <h3>🤝 Most Connected</h3>
                            <div class="leaderboard-list">
                                {self._render_leaderboard(leaderboards.get('connections', []), 'connections')}
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Member Grid -->
                <section class="member-grid-section" id="memberGridSection">
                    <div class="section-header">
                        <h2>🌟 Club Members</h2>
                        <div class="results-info">
                            <span id="memberResultsCount">{len(members)} members found</span>
                        </div>
                    </div>
                    <div class="member-grid" id="memberGrid">
                        {self._render_member_grid(members, user_connections, achievements)}
                    </div>
                </section>
            </div>

            <!-- Member Profile Modal -->
            <div id="memberProfileModal" class="modal large">
                <div class="modal-content member-profile">
                    <div class="modal-header">
                        <h2 id="modalMemberName">Member Profile</h2>
                        <span class="close" onclick="closeMemberModal()">&times;</span>
                    </div>
                    <div class="modal-body" id="memberProfileContent">
                        <!-- Content loaded dynamically -->
                    </div>
                </div>
            </div>

            <!-- Message Modal -->
            <div id="messageModal" class="modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>💬 Send Message</h2>
                        <span class="close" onclick="closeMessageModal()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <form id="messageForm">
                            <div class="form-group">
                                <label for="messageSubject">Subject</label>
                                <input type="text" id="messageSubject" required>
                            </div>
                            <div class="form-group">
                                <label for="messageContent">Message</label>
                                <textarea id="messageContent" rows="6" required></textarea>
                            </div>
                            <div class="form-actions">
                                <button type="button" onclick="closeMessageModal()">Cancel</button>
                                <button type="submit">Send Message</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <script src="/static/js/member_directory.js"></script>
            <script>
                // Initialize directory data
                window.directoryData = {{
                    members: {json.dumps(members)},
                    userConnections: {json.dumps(user_connections)},
                    achievements: {json.dumps(achievements)},
                    leaderboards: {json.dumps(leaderboards)}
                }};

                // Initialize directory
                document.addEventListener('DOMContentLoaded', function() {{
                    initializeMemberDirectory();
                }});
            </script>
        </body>
        </html>
        """

        return web.Response(
            text=html_content,
            content_type='text/html'
        )

    def _get_all_members(self) -> List[Dict[str, Any]]:
        """Get all club members with their profiles and statistics."""
        try:
            cursor = self.db.conn.execute("""
                SELECT
                    u.id, u.username, u.email, u.created_at,
                    mp.display_name, mp.bio, mp.location, mp.specialization,
                    mp.experience_level, mp.profile_image, mp.verified,
                    COALESCE(AVG(tp.total_return), 0) as avg_return,
                    COALESCE(AVG(tp.win_rate), 0) as avg_win_rate,
                    COALESCE(AVG(tp.sharpe_ratio), 0) as avg_sharpe,
                    COALESCE(COUNT(DISTINCT sf.follower_id), 0) as followers_count,
                    COALESCE(COUNT(DISTINCT sp.id), 0) as strategies_count,
                    COALESCE(MAX(tp.date), '1970-01-01') as last_activity
                FROM users u
                LEFT JOIN member_profiles mp ON u.id = mp.user_id
                LEFT JOIN trading_performance tp ON u.id = tp.user_id
                LEFT JOIN strategy_following sf ON u.id = sf.strategy_creator_id
                LEFT JOIN strategy_proposals sp ON u.id = sp.user_id AND sp.status = 'approved'
                WHERE u.role IN ('member', 'admin')
                GROUP BY u.id, u.username, u.email, u.created_at,
                         mp.display_name, mp.bio, mp.location, mp.specialization,
                         mp.experience_level, mp.profile_image, mp.verified
                ORDER BY avg_return DESC, followers_count DESC
            """)

            members = []
            for row in cursor.fetchall():
                members.append({
                    'id': row[0],
                    'username': row[1],
                    'email': row[2],
                    'created_at': row[3],
                    'display_name': row[4] or row[1],
                    'bio': row[5] or '',
                    'location': row[6] or '',
                    'specialization': row[7] or 'general',
                    'experience_level': row[8] or 'beginner',
                    'profile_image': row[9],
                    'verified': bool(row[10]),
                    'avg_return': row[11],
                    'avg_win_rate': row[12],
                    'avg_sharpe': row[13],
                    'followers_count': row[14],
                    'strategies_count': row[15],
                    'last_activity': row[16],
                    'reputation_score': self._calculate_reputation_score(row[11], row[12], row[14], row[15])
                })

            return members

        except Exception as e:
            logger.error(f"Error getting all members: {e}")
            return []

    def _get_leaderboards(self) -> Dict[str, List[Dict[str, Any]]]:
        """Get various leaderboards."""
        try:
            leaderboards = {}

            # Performance leaderboard
            cursor = self.db.conn.execute("""
                SELECT
                    u.id, u.username, mp.display_name,
                    AVG(tp.total_return) as avg_return,
                    COUNT(tp.date) as trading_days
                FROM users u
                LEFT JOIN member_profiles mp ON u.id = mp.user_id
                LEFT JOIN trading_performance tp ON u.id = tp.user_id
                WHERE tp.total_return IS NOT NULL
                GROUP BY u.id, u.username, mp.display_name
                HAVING trading_days >= 10
                ORDER BY avg_return DESC
                LIMIT 10
            """)

            leaderboards['performance'] = []
            for row in cursor.fetchall():
                leaderboards['performance'].append({
                    'id': row[0],
                    'username': row[1],
                    'display_name': row[2] or row[1],
                    'value': row[3],
                    'trading_days': row[4]
                })

            # Consistency leaderboard (lowest volatility)
            cursor = self.db.conn.execute("""
                SELECT
                    u.id, u.username, mp.display_name,
                    AVG(tp.volatility) as avg_volatility,
                    AVG(tp.total_return) as avg_return
                FROM users u
                LEFT JOIN member_profiles mp ON u.id = mp.user_id
                LEFT JOIN trading_performance tp ON u.id = tp.user_id
                WHERE tp.volatility IS NOT NULL AND tp.total_return > 0
                GROUP BY u.id, u.username, mp.display_name
                ORDER BY avg_volatility ASC, avg_return DESC
                LIMIT 10
            """)

            leaderboards['consistency'] = []
            for row in cursor.fetchall():
                leaderboards['consistency'].append({
                    'id': row[0],
                    'username': row[1],
                    'display_name': row[2] or row[1],
                    'value': row[3],
                    'avg_return': row[4]
                })

            # Risk management leaderboard (best Sharpe ratio)
            cursor = self.db.conn.execute("""
                SELECT
                    u.id, u.username, mp.display_name,
                    AVG(tp.sharpe_ratio) as avg_sharpe,
                    AVG(tp.total_return) as avg_return
                FROM users u
                LEFT JOIN member_profiles mp ON u.id = mp.user_id
                LEFT JOIN trading_performance tp ON u.id = tp.user_id
                WHERE tp.sharpe_ratio IS NOT NULL
                GROUP BY u.id, u.username, mp.display_name
                ORDER BY avg_sharpe DESC
                LIMIT 10
            """)

            leaderboards['risk_management'] = []
            for row in cursor.fetchall():
                leaderboards['risk_management'].append({
                    'id': row[0],
                    'username': row[1],
                    'display_name': row[2] or row[1],
                    'value': row[3],
                    'avg_return': row[4]
                })

            # Connections leaderboard
            cursor = self.db.conn.execute("""
                SELECT
                    u.id, u.username, mp.display_name,
                    COUNT(DISTINCT sf.follower_id) as followers_count,
                    COUNT(DISTINCT sp.id) as strategies_count
                FROM users u
                LEFT JOIN member_profiles mp ON u.id = mp.user_id
                LEFT JOIN strategy_following sf ON u.id = sf.strategy_creator_id
                LEFT JOIN strategy_proposals sp ON u.id = sp.user_id AND sp.status = 'approved'
                GROUP BY u.id, u.username, mp.display_name
                ORDER BY followers_count DESC, strategies_count DESC
                LIMIT 10
            """)

            leaderboards['connections'] = []
            for row in cursor.fetchall():
                leaderboards['connections'].append({
                    'id': row[0],
                    'username': row[1],
                    'display_name': row[2] or row[1],
                    'value': row[3],
                    'strategies_count': row[4]
                })

            return leaderboards

        except Exception as e:
            logger.error(f"Error getting leaderboards: {e}")
            return {}

    def _get_member_stats(self) -> Dict[str, Any]:
        """Get overall member statistics."""
        try:
            cursor = self.db.conn.execute("""
                SELECT
                    COUNT(DISTINCT u.id) as total_members,
                    COUNT(DISTINCT CASE WHEN tp.date >= date('now', '-30 days') THEN u.id END) as active_traders,
                    COALESCE(AVG(tp.total_return), 0) as avg_performance,
                    COUNT(DISTINCT sf.id) as total_connections
                FROM users u
                LEFT JOIN trading_performance tp ON u.id = tp.user_id
                LEFT JOIN strategy_following sf ON u.id = sf.follower_id OR u.id = sf.strategy_creator_id
                WHERE u.role IN ('member', 'admin')
            """)

            row = cursor.fetchone()
            return {
                'total_members': row[0] or 0,
                'active_traders': row[1] or 0,
                'avg_performance': row[2] or 0.0,
                'total_connections': row[3] or 0
            }

        except Exception as e:
            logger.error(f"Error getting member stats: {e}")
            return {
                'total_members': 0,
                'active_traders': 0,
                'avg_performance': 0.0,
                'total_connections': 0
            }

    def _get_user_connections(self, user_id: int) -> List[int]:
        """Get user's connections (following/followers)."""
        try:
            cursor = self.db.conn.execute("""
                SELECT DISTINCT strategy_creator_id FROM strategy_following
                WHERE follower_id = ? AND is_active = TRUE
            """, (user_id,))

            return [row[0] for row in cursor.fetchall()]

        except Exception as e:
            logger.error(f"Error getting user connections: {e}")
            return []

    def _get_member_achievements(self) -> Dict[int, List[Dict[str, Any]]]:
        """Get achievements for all members."""
        try:
            achievements = {}

            # Define achievement criteria
            achievement_queries = {
                'top_performer': """
                    SELECT user_id FROM trading_performance
                    WHERE total_return > 20
                    GROUP BY user_id
                    HAVING COUNT(*) >= 30
                """,
                'consistent_trader': """
                    SELECT user_id FROM trading_performance
                    WHERE volatility < 0.15 AND total_return > 0
                    GROUP BY user_id
                    HAVING COUNT(*) >= 50
                """,
                'strategy_master': """
                    SELECT user_id FROM strategy_proposals
                    WHERE status = 'approved'
                    GROUP BY user_id
                    HAVING COUNT(*) >= 3
                """,
                'social_leader': """
                    SELECT strategy_creator_id as user_id FROM strategy_following
                    GROUP BY strategy_creator_id
                    HAVING COUNT(*) >= 10
                """,
                'veteran_trader': """
                    SELECT id as user_id FROM users
                    WHERE created_at <= date('now', '-1 year')
                """
            }

            for achievement_name, query in achievement_queries.items():
                cursor = self.db.conn.execute(query)
                for row in cursor.fetchall():
                    user_id = row[0]
                    if user_id not in achievements:
                        achievements[user_id] = []
                    achievements[user_id].append({
                        'name': achievement_name,
                        'title': self._get_achievement_title(achievement_name),
                        'icon': self._get_achievement_icon(achievement_name)
                    })

            return achievements

        except Exception as e:
            logger.error(f"Error getting member achievements: {e}")
            return {}

    def _calculate_reputation_score(self, avg_return: float, win_rate: float, followers: int, strategies: int) -> int:
        """Calculate member reputation score."""
        score = 0

        # Performance component (0-40 points)
        if avg_return > 0:
            score += min(40, int(avg_return * 2))

        # Win rate component (0-30 points)
        if win_rate > 0:
            score += min(30, int(win_rate * 0.3))

        # Social component (0-20 points)
        score += min(20, followers * 2)

        # Contribution component (0-10 points)
        score += min(10, strategies * 3)

        return min(100, score)

    def _get_achievement_title(self, achievement_name: str) -> str:
        """Get human-readable achievement title."""
        titles = {
            'top_performer': 'Top Performer',
            'consistent_trader': 'Consistent Trader',
            'strategy_master': 'Strategy Master',
            'social_leader': 'Social Leader',
            'veteran_trader': 'Veteran Trader'
        }
        return titles.get(achievement_name, achievement_name.replace('_', ' ').title())

    def _get_achievement_icon(self, achievement_name: str) -> str:
        """Get achievement icon."""
        icons = {
            'top_performer': '🏆',
            'consistent_trader': '🎯',
            'strategy_master': '🧠',
            'social_leader': '👑',
            'veteran_trader': '⭐'
        }
        return icons.get(achievement_name, '🏅')

    def _render_leaderboard(self, leaderboard_data: List[Dict[str, Any]], leaderboard_type: str) -> str:
        """Render leaderboard list."""
        if not leaderboard_data:
            return '<div class="empty-leaderboard">No data available</div>'

        leaderboard_html = ""
        for i, member in enumerate(leaderboard_data):
            rank = i + 1
            rank_class = f"rank-{rank}" if rank <= 3 else "rank-other"

            if leaderboard_type == 'performance':
                value_display = f"{member['value']:+.2f}%"
            elif leaderboard_type == 'consistency':
                value_display = f"{member['value']:.3f}"
            elif leaderboard_type == 'risk':
                value_display = f"{member['value']:.2f}"
            else:  # connections
                value_display = str(int(member['value']))

            leaderboard_html += f"""
            <div class="leaderboard-item {rank_class}">
                <div class="rank-badge">{rank}</div>
                <div class="member-info">
                    <div class="member-name">{member['display_name']}</div>
                    <div class="member-value">{value_display}</div>
                </div>
                <button class="view-profile-btn" onclick="viewMemberProfile({member['id']})">View</button>
            </div>
            """

        return leaderboard_html

    def _render_member_grid(self, members: List[Dict[str, Any]], user_connections: List[int], achievements: Dict[int, List[Dict[str, Any]]]) -> str:
        """Render member grid."""
        if not members:
            return '<div class="empty-state">No members found</div>'

        grid_html = ""
        for member in members:
            is_connected = member['id'] in user_connections
            member_achievements = achievements.get(member['id'], [])
            performance_class = "positive" if member['avg_return'] > 0 else "negative"

            # Experience level styling
            exp_class = f"exp-{member['experience_level']}"

            grid_html += f"""
            <div class="member-card" data-member-id="{member['id']}"
                 data-experience="{member['experience_level']}"
                 data-specialization="{member['specialization']}"
                 data-performance="{member['avg_return']}"
                 data-connections="{member['followers_count']}">

                <div class="member-card-header">
                    <div class="member-avatar">
                        {member['display_name'][0].upper() if member['display_name'] else 'M'}
                        {('<div class="verified-badge">✓</div>' if member['verified'] else '')}
                    </div>
                    <div class="member-actions">
                        <button class="action-btn" onclick="sendMessage({member['id']})" title="Send message">💬</button>
                        <button class="action-btn {'connected' if is_connected else ''}"
                                onclick="toggleConnection({member['id']})"
                                title="{'Disconnect' if is_connected else 'Connect'}">
                            {'🤝' if is_connected else '👋'}
                        </button>
                    </div>
                </div>

                <div class="member-info">
                    <h4 class="member-name">{member['display_name']}</h4>
                    <div class="member-meta">
                        <span class="experience-badge {exp_class}">{member['experience_level'].title()}</span>
                        <span class="specialization-badge">{member['specialization'].replace('_', ' ').title()}</span>
                    </div>
                    {('<div class="member-location">📍 ' + member['location'] + '</div>' if member['location'] else '')}
                </div>

                <div class="member-stats">
                    <div class="stat-row">
                        <div class="stat">
                            <span class="stat-label">Performance</span>
                            <span class="stat-value {performance_class}">{member['avg_return']:+.2f}%</span>
                        </div>
                        <div class="stat">
                            <span class="stat-label">Win Rate</span>
                            <span class="stat-value">{member['avg_win_rate']:.1f}%</span>
                        </div>
                    </div>
                    <div class="stat-row">
                        <div class="stat">
                            <span class="stat-label">Reputation</span>
                            <span class="stat-value">{member['reputation_score']}/100</span>
                        </div>
                        <div class="stat">
                            <span class="stat-label">Followers</span>
                            <span class="stat-value">{member['followers_count']}</span>
                        </div>
                    </div>
                </div>

                {('<div class="member-bio">' + (member['bio'][:80] + '...' if len(member['bio']) > 80 else member['bio']) + '</div>' if member['bio'] else '')}

                <div class="member-achievements">
                    {self._render_member_achievements(member_achievements[:3])}
                </div>

                <div class="member-footer">
                    <div class="member-activity">
                        <span class="activity-label">Last active:</span>
                        <span class="activity-time">{self._format_time_ago(member['last_activity'])}</span>
                    </div>
                    <div class="member-strategies">
                        {member['strategies_count']} strategies
                    </div>
                </div>

                <div class="member-card-actions">
                    <button class="btn-view" onclick="viewMemberProfile({member['id']})">View Profile</button>
                    <button class="btn-connect {'active' if is_connected else ''}"
                            onclick="toggleConnection({member['id']})">
                        {'Disconnect' if is_connected else 'Connect'}
                    </button>
                </div>
            </div>
            """

        return grid_html

    def _render_member_achievements(self, achievements: List[Dict[str, Any]]) -> str:
        """Render member achievements."""
        if not achievements:
            return '<div class="no-achievements">No achievements yet</div>'

        achievements_html = ""
        for achievement in achievements:
            achievements_html += f"""
            <div class="achievement-badge" title="{achievement['title']}">
                {achievement['icon']}
            </div>
            """

        return achievements_html

    def _format_time_ago(self, timestamp: str) -> str:
        """Format timestamp as time ago."""
        try:
            if isinstance(timestamp, str):
                if timestamp == '1970-01-01':
                    return "Never"
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            else:
                dt = timestamp

            now = datetime.now()
            diff = now - dt

            if diff.days > 30:
                months = diff.days // 30
                return f"{months} month{'s' if months != 1 else ''} ago"
            elif diff.days > 0:
                return f"{diff.days} day{'s' if diff.days != 1 else ''} ago"
            elif diff.seconds > 3600:
                hours = diff.seconds // 3600
                return f"{hours} hour{'s' if hours != 1 else ''} ago"
            elif diff.seconds > 60:
                minutes = diff.seconds // 60
                return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
            else:
                return "Just now"
        except:
            return "Unknown"
