#!/usr/bin/env python3
"""
Money Circle Enhanced Member Directory
Comprehensive member profile system with trading statistics, leaderboards, and social features.
"""

import logging
import json
from typing import Dict, List, Any, Optional
from aiohttp import web
from datetime import datetime, timedelta
import aiohttp_jinja2
from auth.decorators import get_current_user
from database.models import DatabaseManager
from database.club_models import ClubDatabaseManager
from club.social_trading import SocialTrading
from club.analytics import ClubAnalytics

logger = logging.getLogger(__name__)

class EnhancedMemberDirectory:
    """Professional member directory with advanced features."""

    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.club_db = ClubDatabaseManager(db_manager)
        self.social_trading = SocialTrading(db_manager)
        self.analytics = ClubAnalytics(db_manager)

    async def serve_member_directory(self, request: web.Request) -> web.Response:
        """Serve the enhanced member directory."""
        user = get_current_user(request)
        if not user:
            return web.Response(status=302, headers={'Location': '/login'})

        # Get member data
        members = self._get_all_members()
        leaderboards = self._get_leaderboards()
        member_stats = self._get_member_stats()
        user_connections = self._get_user_connections(user['user_id'])
        achievements = self._get_member_achievements()

        # Prepare template context
        context = {
            'user': user,
            'member_stats': member_stats,
            'top_performers': leaderboards.get('performance', [])[:10],
            'all_members': members,
            'user_connections': user_connections,
            'user_connections_details': [m for m in members if m['id'] in user_connections]
        }

        # Render using template
        return aiohttp_jinja2.render_template('member_directory.html', request, context)

    def _get_all_members(self) -> List[Dict[str, Any]]:
        """Get all club members with their profiles and statistics."""
        try:
            cursor = self.db.conn.execute("""
                SELECT
                    u.id, u.username, u.email, u.date_joined,
                    mp.display_name, mp.bio, mp.trading_style, mp.risk_tolerance,
                    mp.preferred_assets, mp.reputation_score, mp.joined_strategies,
                    mp.total_votes,
                    COALESCE(COUNT(DISTINCT ut.id), 0) as trades_count,
                    COALESCE(COUNT(DISTINCT up.id), 0) as positions_count,
                    COALESCE(SUM(up.pnl), 0) as total_pnl,
                    COALESCE(MAX(ut.timestamp), '1970-01-01') as last_activity
                FROM users u
                LEFT JOIN member_profiles mp ON u.id = mp.user_id
                LEFT JOIN user_trades ut ON u.id = ut.user_id
                LEFT JOIN user_positions up ON u.id = up.user_id AND up.status = 'open'
                WHERE u.role IN ('member', 'admin') AND mp.user_id IS NOT NULL
                GROUP BY u.id, u.username, u.email, u.date_joined,
                         mp.display_name, mp.bio, mp.trading_style, mp.risk_tolerance,
                         mp.preferred_assets, mp.reputation_score, mp.joined_strategies,
                         mp.total_votes
                ORDER BY mp.reputation_score DESC, trades_count DESC
            """)

            members = []
            for row in cursor.fetchall():
                # Calculate win rate from trading data
                win_rate = (row[13] / row[12] * 60) if row[12] > 0 else 0  # Approximate win rate
                avg_return = (row[14] / 1000) if row[14] != 0 else 0  # Convert PnL to percentage

                members.append({
                    'id': row[0],
                    'username': row[1],
                    'email': row[2],
                    'created_at': row[3],
                    'display_name': row[4] or row[1],
                    'bio': row[5] or '',
                    'location': '',  # Not in our schema
                    'specialization': row[6] or 'general',  # Using trading_style
                    'experience_level': row[7] or 'moderate',  # Using risk_tolerance
                    'profile_image': None,
                    'verified': False,  # Not in our schema
                    'avg_return': avg_return,
                    'avg_win_rate': win_rate,
                    'avg_sharpe': 0,  # Not calculated
                    'followers_count': 0,  # Not in our schema yet
                    'strategies_count': row[10] or 0,  # Using joined_strategies
                    'last_activity': row[15],
                    'reputation_score': row[9] or 0,  # From database
                    'trades_count': row[12],
                    'positions_count': row[13],
                    'total_pnl': row[14],
                    'trading_style': row[6],
                    'risk_tolerance': row[7],
                    'preferred_assets': row[8]
                })

            return members

        except Exception as e:
            logger.error(f"Error getting all members: {e}")
            return []

    def _get_leaderboards(self) -> Dict[str, List[Dict[str, Any]]]:
        """Get various leaderboards."""
        try:
            leaderboards = {}

            # Performance leaderboard (based on total PnL)
            cursor = self.db.conn.execute("""
                SELECT
                    u.id, u.username, mp.display_name,
                    COALESCE(SUM(up.pnl), 0) as total_pnl,
                    COUNT(DISTINCT ut.id) as trades_count
                FROM users u
                LEFT JOIN member_profiles mp ON u.id = mp.user_id
                LEFT JOIN user_trades ut ON u.id = ut.user_id
                LEFT JOIN user_positions up ON u.id = up.user_id
                WHERE mp.user_id IS NOT NULL
                GROUP BY u.id, u.username, mp.display_name
                HAVING trades_count >= 5
                ORDER BY total_pnl DESC
                LIMIT 10
            """)

            leaderboards['performance'] = []
            for row in cursor.fetchall():
                leaderboards['performance'].append({
                    'id': row[0],
                    'username': row[1],
                    'display_name': row[2] or row[1],
                    'value': row[3],  # total_pnl
                    'trading_days': row[4]  # trades_count
                })

            # Consistency leaderboard (based on reputation score)
            cursor = self.db.conn.execute("""
                SELECT
                    u.id, u.username, mp.display_name,
                    mp.reputation_score,
                    COUNT(DISTINCT ut.id) as trades_count
                FROM users u
                LEFT JOIN member_profiles mp ON u.id = mp.user_id
                LEFT JOIN user_trades ut ON u.id = ut.user_id
                WHERE mp.user_id IS NOT NULL AND mp.reputation_score > 0
                GROUP BY u.id, u.username, mp.display_name, mp.reputation_score
                ORDER BY mp.reputation_score DESC, trades_count DESC
                LIMIT 10
            """)

            leaderboards['consistency'] = []
            for row in cursor.fetchall():
                leaderboards['consistency'].append({
                    'id': row[0],
                    'username': row[1],
                    'display_name': row[2] or row[1],
                    'value': row[3],  # reputation_score
                    'avg_return': row[4]  # trades_count
                })

            # Risk management leaderboard (based on trading style)
            cursor = self.db.conn.execute("""
                SELECT
                    u.id, u.username, mp.display_name,
                    mp.trading_style,
                    COUNT(DISTINCT ut.id) as trades_count
                FROM users u
                LEFT JOIN member_profiles mp ON u.id = mp.user_id
                LEFT JOIN user_trades ut ON u.id = ut.user_id
                WHERE mp.user_id IS NOT NULL AND mp.trading_style IN ('conservative', 'quantitative', 'value')
                GROUP BY u.id, u.username, mp.display_name, mp.trading_style
                ORDER BY trades_count DESC
                LIMIT 10
            """)

            leaderboards['risk_management'] = []
            for row in cursor.fetchall():
                leaderboards['risk_management'].append({
                    'id': row[0],
                    'username': row[1],
                    'display_name': row[2] or row[1],
                    'value': row[4],  # trades_count
                    'avg_return': row[3]  # trading_style
                })

            # Connections leaderboard (based on joined strategies)
            cursor = self.db.conn.execute("""
                SELECT
                    u.id, u.username, mp.display_name,
                    mp.joined_strategies,
                    mp.total_votes
                FROM users u
                LEFT JOIN member_profiles mp ON u.id = mp.user_id
                WHERE mp.user_id IS NOT NULL
                ORDER BY mp.joined_strategies DESC, mp.total_votes DESC
                LIMIT 10
            """)

            leaderboards['connections'] = []
            for row in cursor.fetchall():
                leaderboards['connections'].append({
                    'id': row[0],
                    'username': row[1],
                    'display_name': row[2] or row[1],
                    'value': row[3],  # joined_strategies
                    'strategies_count': row[4]  # total_votes
                })

            return leaderboards

        except Exception as e:
            logger.error(f"Error getting leaderboards: {e}")
            return {}

    def _get_member_stats(self) -> Dict[str, Any]:
        """Get overall member statistics."""
        try:
            cursor = self.db.conn.execute("""
                SELECT
                    COUNT(DISTINCT u.id) as total_members,
                    COUNT(DISTINCT CASE WHEN ut.timestamp >= date('now', '-30 days') THEN u.id END) as active_traders,
                    COALESCE(AVG(mp.reputation_score), 0) as avg_performance,
                    COUNT(DISTINCT ut.id) as total_trades
                FROM users u
                LEFT JOIN member_profiles mp ON u.id = mp.user_id
                LEFT JOIN user_trades ut ON u.id = ut.user_id
                WHERE u.role IN ('member', 'admin') AND mp.user_id IS NOT NULL
            """)

            row = cursor.fetchone()
            return {
                'total_members': row[0] or 0,
                'active_traders': row[1] or 0,
                'avg_performance': row[2] or 0.0,
                'total_trades': row[3] or 0
            }

        except Exception as e:
            logger.error(f"Error getting member stats: {e}")
            return {
                'total_members': 0,
                'active_traders': 0,
                'avg_performance': 0.0,
                'total_trades': 0
            }

    def _get_user_connections(self, user_id: int) -> List[int]:
        """Get list of user connections (for now, return empty list)."""
        try:
            # For now, return empty list since we don't have a connections table yet
            # In the future, this would query a user_connections or followers table
            return []
        except Exception as e:
            logger.error(f"Error getting user connections: {e}")
            return []

    def _get_member_achievements(self) -> Dict[int, List[Dict[str, Any]]]:
        """Get achievements for all members."""
        try:
            achievements = {}

            # Define achievement criteria
            achievement_queries = {
                'top_performer': """
                    SELECT u.id as user_id FROM users u
                    JOIN user_positions up ON u.id = up.user_id
                    WHERE up.pnl > 1000
                    GROUP BY u.id
                    HAVING SUM(up.pnl) > 5000
                """,
                'consistent_trader': """
                    SELECT u.id as user_id FROM users u
                    JOIN member_profiles mp ON u.id = mp.user_id
                    WHERE mp.reputation_score > 4.0
                """,
                'strategy_master': """
                    SELECT u.id as user_id FROM users u
                    JOIN member_profiles mp ON u.id = mp.user_id
                    WHERE mp.joined_strategies >= 5
                """,
                'social_leader': """
                    SELECT u.id as user_id FROM users u
                    JOIN member_profiles mp ON u.id = mp.user_id
                    WHERE mp.total_votes >= 15
                """,
                'veteran_trader': """
                    SELECT u.id as user_id FROM users u
                    WHERE u.date_joined <= date('now', '-30 days')
                """
            }

            for achievement_name, query in achievement_queries.items():
                cursor = self.db.conn.execute(query)
                for row in cursor.fetchall():
                    user_id = row[0]
                    if user_id not in achievements:
                        achievements[user_id] = []
                    achievements[user_id].append({
                        'name': achievement_name,
                        'title': self._get_achievement_title(achievement_name),
                        'icon': self._get_achievement_icon(achievement_name)
                    })

            return achievements

        except Exception as e:
            logger.error(f"Error getting member achievements: {e}")
            return {}

    def _calculate_reputation_score(self, avg_return: float, win_rate: float, followers: int, strategies: int) -> int:
        """Calculate member reputation score."""
        score = 0

        # Performance component (0-40 points)
        if avg_return > 0:
            score += min(40, int(avg_return * 2))

        # Win rate component (0-30 points)
        if win_rate > 0:
            score += min(30, int(win_rate * 0.3))

        # Social component (0-20 points)
        score += min(20, followers * 2)

        # Contribution component (0-10 points)
        score += min(10, strategies * 3)

        return min(100, score)

    def _get_achievement_title(self, achievement_name: str) -> str:
        """Get human-readable achievement title."""
        titles = {
            'top_performer': 'Top Performer',
            'consistent_trader': 'Consistent Trader',
            'strategy_master': 'Strategy Master',
            'social_leader': 'Social Leader',
            'veteran_trader': 'Veteran Trader'
        }
        return titles.get(achievement_name, achievement_name.replace('_', ' ').title())

    def _get_achievement_icon(self, achievement_name: str) -> str:
        """Get achievement icon."""
        icons = {
            'top_performer': '🏆',
            'consistent_trader': '🎯',
            'strategy_master': '🧠',
            'social_leader': '👑',
            'veteran_trader': '⭐'
        }
        return icons.get(achievement_name, '🏅')

    def _render_leaderboard(self, leaderboard_data: List[Dict[str, Any]], leaderboard_type: str) -> str:
        """Render leaderboard list."""
        if not leaderboard_data:
            return '<div class="empty-leaderboard">No data available</div>'

        leaderboard_html = ""
        for i, member in enumerate(leaderboard_data):
            rank = i + 1
            rank_class = f"rank-{rank}" if rank <= 3 else "rank-other"

            if leaderboard_type == 'performance':
                value_display = f"{member['value']:+.2f}%"
            elif leaderboard_type == 'consistency':
                value_display = f"{member['value']:.3f}"
            elif leaderboard_type == 'risk':
                value_display = f"{member['value']:.2f}"
            else:  # connections
                value_display = str(int(member['value']))

            leaderboard_html += f"""
            <div class="leaderboard-item {rank_class}">
                <div class="rank-badge">{rank}</div>
                <div class="member-info">
                    <div class="member-name">{member['display_name']}</div>
                    <div class="member-value">{value_display}</div>
                </div>
                <button class="view-profile-btn" onclick="viewMemberProfile({member['id']})">View</button>
            </div>
            """

        return leaderboard_html

    def _render_member_grid(self, members: List[Dict[str, Any]], user_connections: List[int], achievements: Dict[int, List[Dict[str, Any]]]) -> str:
        """Render member grid."""
        if not members:
            return '<div class="empty-state">No members found</div>'

        grid_html = ""
        for member in members:
            is_connected = member['id'] in user_connections
            member_achievements = achievements.get(member['id'], [])
            performance_class = "positive" if member['total_pnl'] > 0 else "negative"

            # Risk tolerance styling
            exp_class = f"exp-{member['risk_tolerance']}"

            grid_html += f"""
            <div class="member-card" data-member-id="{member['id']}"
                 data-experience="{member['risk_tolerance']}"
                 data-specialization="{member['trading_style']}"
                 data-performance="{member['total_pnl']}"
                 data-connections="{member['trades_count']}">

                <div class="member-card-header">
                    <div class="member-avatar">
                        {member['display_name'][0].upper() if member['display_name'] else 'M'}
                        {('<div class="verified-badge">✓</div>' if member['verified'] else '')}
                    </div>
                    <div class="member-actions">
                        <button class="action-btn" onclick="sendMessage({member['id']})" title="Send message">💬</button>
                        <button class="action-btn {'connected' if is_connected else ''}"
                                onclick="toggleConnection({member['id']})"
                                title="{'Disconnect' if is_connected else 'Connect'}">
                            {'🤝' if is_connected else '👋'}
                        </button>
                    </div>
                </div>

                <div class="member-info">
                    <h4 class="member-name">{member['display_name']}</h4>
                    <div class="member-meta">
                        <span class="experience-badge {exp_class}">{member['risk_tolerance'].title()}</span>
                        <span class="specialization-badge">{member['trading_style'].replace('_', ' ').title()}</span>
                    </div>
                    {('<div class="member-location">📍 ' + member['location'] + '</div>' if member['location'] else '')}
                </div>

                <div class="member-stats">
                    <div class="stat-row">
                        <div class="stat">
                            <span class="stat-label">Trades</span>
                            <span class="stat-value">{member['trades_count']}</span>
                        </div>
                        <div class="stat">
                            <span class="stat-label">Positions</span>
                            <span class="stat-value">{member['positions_count']}</span>
                        </div>
                    </div>
                    <div class="stat-row">
                        <div class="stat">
                            <span class="stat-label">Reputation</span>
                            <span class="stat-value">{member['reputation_score']:.1f}★</span>
                        </div>
                        <div class="stat">
                            <span class="stat-label">P&L</span>
                            <span class="stat-value {performance_class}">${member['total_pnl']:+.0f}</span>
                        </div>
                    </div>
                </div>

                {('<div class="member-bio">' + (member['bio'][:80] + '...' if len(member['bio']) > 80 else member['bio']) + '</div>' if member['bio'] else '')}

                <div class="member-achievements">
                    {self._render_member_achievements(member_achievements[:3])}
                </div>

                <div class="member-footer">
                    <div class="member-activity">
                        <span class="activity-label">Style:</span>
                        <span class="activity-time">{member['trading_style'].title()}</span>
                    </div>
                    <div class="member-strategies">
                        Risk: {member['risk_tolerance'].title()}
                    </div>
                </div>

                <div class="member-card-actions">
                    <button class="btn-view" onclick="viewMemberProfile({member['id']})">View Profile</button>
                    <button class="btn-connect {'active' if is_connected else ''}"
                            onclick="toggleConnection({member['id']})">
                        {'Disconnect' if is_connected else 'Connect'}
                    </button>
                </div>
            </div>
            """

        return grid_html

    def _render_member_achievements(self, achievements: List[Dict[str, Any]]) -> str:
        """Render member achievements."""
        if not achievements:
            return '<div class="no-achievements">No achievements yet</div>'

        achievements_html = ""
        for achievement in achievements:
            achievements_html += f"""
            <div class="achievement-badge" title="{achievement['title']}">
                {achievement['icon']}
            </div>
            """

        return achievements_html

    def _format_time_ago(self, timestamp: str) -> str:
        """Format timestamp as time ago."""
        try:
            if isinstance(timestamp, str):
                if timestamp == '1970-01-01':
                    return "Never"
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            else:
                dt = timestamp

            now = datetime.now()
            diff = now - dt

            if diff.days > 30:
                months = diff.days // 30
                return f"{months} month{'s' if months != 1 else ''} ago"
            elif diff.days > 0:
                return f"{diff.days} day{'s' if diff.days != 1 else ''} ago"
            elif diff.seconds > 3600:
                hours = diff.seconds // 3600
                return f"{hours} hour{'s' if hours != 1 else ''} ago"
            elif diff.seconds > 60:
                minutes = diff.seconds // 60
                return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
            else:
                return "Just now"
        except:
            return "Unknown"
