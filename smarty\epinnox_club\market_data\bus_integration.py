#!/usr/bin/env python3
"""
Money Circle Market Data Integration
Connects to the existing SQLite bus system for live market data.
"""

import asyncio
import sqlite3
import json
import logging
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
import aiosqlite

logger = logging.getLogger(__name__)

class MarketDataBus:
    """Integration with the existing SQLite bus for market data."""
    
    def __init__(self, bus_db_path: str = "../data/bus.db"):
        self.bus_db_path = bus_db_path
        self.subscribers: Dict[str, List[Callable]] = {}
        self.running = False
        self.poll_interval = 1.0  # Poll every second
        self.last_timestamps: Dict[str, float] = {}
        
    async def connect(self) -> bool:
        """Connect to the SQLite bus."""
        try:
            # Test connection
            async with aiosqlite.connect(self.bus_db_path) as db:
                cursor = await db.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = await cursor.fetchall()
                logger.info(f"✅ Connected to SQLite bus, found {len(tables)} tables")
                return True
        except Exception as e:
            logger.error(f"❌ Failed to connect to SQLite bus: {e}")
            return False
    
    def subscribe(self, topic: str, callback: Callable[[Dict[str, Any]], None]):
        """Subscribe to market data updates for a specific topic."""
        if topic not in self.subscribers:
            self.subscribers[topic] = []
        
        self.subscribers[topic].append(callback)
        logger.info(f"📡 Subscribed to topic: {topic}")
    
    def unsubscribe(self, topic: str, callback: Callable):
        """Unsubscribe from market data updates."""
        if topic in self.subscribers and callback in self.subscribers[topic]:
            self.subscribers[topic].remove(callback)
            logger.info(f"📡 Unsubscribed from topic: {topic}")
    
    async def start_polling(self):
        """Start polling the SQLite bus for new data."""
        self.running = True
        logger.info("🚀 Starting market data polling...")
        
        while self.running:
            try:
                await self._poll_bus_data()
                await asyncio.sleep(self.poll_interval)
            except Exception as e:
                logger.error(f"Market data polling error: {e}")
                await asyncio.sleep(5)  # Wait longer on error
    
    def stop_polling(self):
        """Stop polling the SQLite bus."""
        self.running = False
        logger.info("🛑 Stopped market data polling")
    
    async def _poll_bus_data(self):
        """Poll the SQLite bus for new data."""
        try:
            async with aiosqlite.connect(self.bus_db_path) as db:
                # Get recent market data
                await self._poll_market_data(db)
                await self._poll_orderbook_data(db)
                await self._poll_trades_data(db)
                await self._poll_strategy_signals(db)
                
        except Exception as e:
            logger.error(f"Bus polling error: {e}")
    
    async def _poll_market_data(self, db: aiosqlite.Connection):
        """Poll for market data updates."""
        try:
            # Get latest market data since last poll
            last_ts = self.last_timestamps.get('market_data', 0)
            
            cursor = await db.execute("""
                SELECT topic, payload, timestamp FROM bus_messages 
                WHERE topic LIKE 'market.%' AND timestamp > ?
                ORDER BY timestamp DESC LIMIT 100
            """, (last_ts,))
            
            rows = await cursor.fetchall()
            
            for row in rows:
                topic, payload_str, timestamp = row
                
                try:
                    payload = json.loads(payload_str)
                    await self._notify_subscribers(topic, payload)
                    self.last_timestamps['market_data'] = max(
                        self.last_timestamps.get('market_data', 0), 
                        timestamp
                    )
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON in market data: {payload_str}")
                    
        except Exception as e:
            logger.error(f"Market data polling error: {e}")
    
    async def _poll_orderbook_data(self, db: aiosqlite.Connection):
        """Poll for order book updates."""
        try:
            last_ts = self.last_timestamps.get('orderbook', 0)
            
            cursor = await db.execute("""
                SELECT topic, payload, timestamp FROM bus_messages 
                WHERE topic LIKE 'orderbook.%' AND timestamp > ?
                ORDER BY timestamp DESC LIMIT 50
            """, (last_ts,))
            
            rows = await cursor.fetchall()
            
            for row in rows:
                topic, payload_str, timestamp = row
                
                try:
                    payload = json.loads(payload_str)
                    await self._notify_subscribers(topic, payload)
                    self.last_timestamps['orderbook'] = max(
                        self.last_timestamps.get('orderbook', 0), 
                        timestamp
                    )
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON in orderbook data: {payload_str}")
                    
        except Exception as e:
            logger.error(f"Orderbook polling error: {e}")
    
    async def _poll_trades_data(self, db: aiosqlite.Connection):
        """Poll for recent trades updates."""
        try:
            last_ts = self.last_timestamps.get('trades', 0)
            
            cursor = await db.execute("""
                SELECT topic, payload, timestamp FROM bus_messages 
                WHERE topic LIKE 'trades.%' AND timestamp > ?
                ORDER BY timestamp DESC LIMIT 50
            """, (last_ts,))
            
            rows = await cursor.fetchall()
            
            for row in rows:
                topic, payload_str, timestamp = row
                
                try:
                    payload = json.loads(payload_str)
                    await self._notify_subscribers(topic, payload)
                    self.last_timestamps['trades'] = max(
                        self.last_timestamps.get('trades', 0), 
                        timestamp
                    )
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON in trades data: {payload_str}")
                    
        except Exception as e:
            logger.error(f"Trades polling error: {e}")
    
    async def _poll_strategy_signals(self, db: aiosqlite.Connection):
        """Poll for strategy signals."""
        try:
            last_ts = self.last_timestamps.get('strategy', 0)
            
            cursor = await db.execute("""
                SELECT topic, payload, timestamp FROM bus_messages 
                WHERE topic LIKE 'strategy.%' AND timestamp > ?
                ORDER BY timestamp DESC LIMIT 20
            """, (last_ts,))
            
            rows = await cursor.fetchall()
            
            for row in rows:
                topic, payload_str, timestamp = row
                
                try:
                    payload = json.loads(payload_str)
                    await self._notify_subscribers(topic, payload)
                    self.last_timestamps['strategy'] = max(
                        self.last_timestamps.get('strategy', 0), 
                        timestamp
                    )
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON in strategy data: {payload_str}")
                    
        except Exception as e:
            logger.error(f"Strategy polling error: {e}")
    
    async def _notify_subscribers(self, topic: str, payload: Dict[str, Any]):
        """Notify subscribers of new data."""
        # Notify exact topic subscribers
        if topic in self.subscribers:
            for callback in self.subscribers[topic]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(payload)
                    else:
                        callback(payload)
                except Exception as e:
                    logger.error(f"Subscriber callback error: {e}")
        
        # Notify wildcard subscribers
        topic_parts = topic.split('.')
        for i in range(len(topic_parts)):
            wildcard_topic = '.'.join(topic_parts[:i+1]) + '.*'
            if wildcard_topic in self.subscribers:
                for callback in self.subscribers[wildcard_topic]:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback(payload)
                        else:
                            callback(payload)
                    except Exception as e:
                        logger.error(f"Wildcard subscriber callback error: {e}")
    
    async def get_latest_market_data(self, symbol: str = "BTC-USDT") -> Optional[Dict[str, Any]]:
        """Get the latest market data for a symbol."""
        try:
            async with aiosqlite.connect(self.bus_db_path) as db:
                cursor = await db.execute("""
                    SELECT payload FROM bus_messages 
                    WHERE topic = ? 
                    ORDER BY timestamp DESC LIMIT 1
                """, (f"market.{symbol}",))
                
                row = await cursor.fetchone()
                if row:
                    return json.loads(row[0])
                return None
                
        except Exception as e:
            logger.error(f"Error getting latest market data: {e}")
            return None
    
    async def get_latest_orderbook(self, symbol: str = "BTC-USDT") -> Optional[Dict[str, Any]]:
        """Get the latest order book for a symbol."""
        try:
            async with aiosqlite.connect(self.bus_db_path) as db:
                cursor = await db.execute("""
                    SELECT payload FROM bus_messages 
                    WHERE topic = ? 
                    ORDER BY timestamp DESC LIMIT 1
                """, (f"orderbook.{symbol}",))
                
                row = await cursor.fetchone()
                if row:
                    return json.loads(row[0])
                return None
                
        except Exception as e:
            logger.error(f"Error getting latest orderbook: {e}")
            return None
    
    async def get_recent_trades(self, symbol: str = "BTC-USDT", limit: int = 20) -> List[Dict[str, Any]]:
        """Get recent trades for a symbol."""
        try:
            async with aiosqlite.connect(self.bus_db_path) as db:
                cursor = await db.execute("""
                    SELECT payload FROM bus_messages 
                    WHERE topic = ? 
                    ORDER BY timestamp DESC LIMIT ?
                """, (f"trades.{symbol}", limit))
                
                rows = await cursor.fetchall()
                trades = []
                
                for row in rows:
                    try:
                        trade_data = json.loads(row[0])
                        trades.append(trade_data)
                    except json.JSONDecodeError:
                        continue
                
                return trades
                
        except Exception as e:
            logger.error(f"Error getting recent trades: {e}")
            return []

# Global market data bus instance
_market_data_bus = None

def get_market_data_bus(bus_db_path: str = "../data/bus.db") -> MarketDataBus:
    """Get global market data bus instance."""
    global _market_data_bus
    if _market_data_bus is None:
        _market_data_bus = MarketDataBus(bus_db_path)
    return _market_data_bus
