2025-05-31 12:58:59,599 - aiohttp.access - INFO - 127.0.0.1 [31/May/2025:11:58:59 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 12:59:01,188 - aiohttp.access - INFO - ::1 [31/May/2025:11:59:01 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 12:59:03,199 - aiohttp.access - INFO - ::1 [31/May/2025:11:59:03 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 12:59:03,201 - aiohttp.access - INFO - ::1 [31/May/2025:11:59:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 12:59:03,205 - aiohttp.access - INFO - ::1 [31/May/2025:11:59:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 12:59:03,206 - aiohttp.access - INFO - ::1 [31/May/2025:11:59:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 12:59:05,194 - aiohttp.access - INFO - ::1 [31/May/2025:11:59:05 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 12:59:07,193 - aiohttp.access - INFO - ::1 [31/May/2025:11:59:07 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 12:59:09,188 - aiohttp.access - INFO - ::1 [31/May/2025:11:59:09 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 12:59:11,186 - aiohttp.access - INFO - ::1 [31/May/2025:11:59:11 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 12:59:13,204 - aiohttp.access - INFO - ::1 [31/May/2025:11:59:13 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 12:59:15,198 - aiohttp.access - INFO - ::1 [31/May/2025:11:59:15 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:09:49,018 - aiohttp.access - INFO - 127.0.0.1 [31/May/2025:12:09:49 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:09:49,190 - aiohttp.access - INFO - ::1 [31/May/2025:12:09:49 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:09:51,279 - aiohttp.access - INFO - ::1 [31/May/2025:12:09:51 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:09:54,203 - aiohttp.access - INFO - ::1 [31/May/2025:12:09:54 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:14:53,301 - aiohttp.access - INFO - ::1 [31/May/2025:12:14:53 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:14:59,198 - aiohttp.access - INFO - ::1 [31/May/2025:12:14:59 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:14:59,778 - aiohttp.access - INFO - ::1 [31/May/2025:12:14:59 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:01,255 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:01 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:03,201 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:03,202 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:03,203 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:05,192 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:05 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:05,206 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:05 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:07,189 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:07 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:11,187 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:11 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:11,200 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:11 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:13,187 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:13 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:17,195 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:17 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:17,208 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:17 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:19,185 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:19 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:23,192 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:23 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:23,205 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:23 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:25,193 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:25 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:29,189 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:29 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:29,204 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:29 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:31,190 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:31 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:35,203 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:35 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:35,219 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:35 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:37,187 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:37 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:41,207 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:41 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:41,220 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:41 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:43,197 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:43 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:47,205 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:47 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:47,221 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:47 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:49,198 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:49 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:53,204 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:53 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:53,219 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:53 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:55,200 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:55 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:59,220 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:59 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:59,236 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:59 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:01,230 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:01 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:03,196 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:03,200 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:03,202 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:05,216 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:05 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:05,232 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:05 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:07,271 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:07 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:11,333 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:11 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:11,363 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:11 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:13,232 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:13 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:17,487 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:17 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:17,502 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:17 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:19,346 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:19 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:23,567 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:23 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:23,779 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:23 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:25,740 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:25 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:29,658 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:29 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:29,752 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:29 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:31,456 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:31 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:36,256 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:36 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:37,129 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:37 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:38,748 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:38 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:46,681 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:46 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:47,002 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:47 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:47,017 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:47 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:53,470 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:53 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:54,526 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:54 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:56,629 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:56 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:00,328 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:00 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:03,193 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:03,200 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:03,202 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:03,830 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:03 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:05,865 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:05 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:06,193 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:06 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:09,188 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:09 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:12,194 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:12 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:14,641 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:14 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:15,188 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:15 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:18,195 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:18 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:21,187 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:21 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:24,182 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:24 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:24,599 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:24 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:27,186 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:27 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:30,196 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:30 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:33,194 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:33 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:33,879 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:33 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:36,205 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:36 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:39,197 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:39 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:41,733 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:41 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:42,204 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:42 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:45,208 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:45 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:48,211 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:48 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:48,808 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:48 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:51,203 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:51 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:54,212 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:54 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:57,213 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:57 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:57,727 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:57 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:00,229 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:00 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:03,196 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:03,201 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:03,203 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:03,204 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:03 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:06,248 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:06 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:07,433 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:07 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:09,254 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:09 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:12,293 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:12 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:14,910 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:14 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:15,253 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:15 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:18,417 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:18 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:21,374 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:21 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:24,745 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:24 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:24,807 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:24 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:27,409 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:27 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:30,711 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:30 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:31,271 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:31 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:34,131 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:34 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:37,744 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:37 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:39,800 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:39 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:41,839 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:41 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:46,489 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:46 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:50,109 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:50 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:50,838 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:50 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:54,775 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:54 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:58,158 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:58 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:59,303 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:59 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:01,738 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:01 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:03,196 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:03,201 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:03,205 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:06,910 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:06 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:07,189 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:07 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:07,473 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:07 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:13,195 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:13 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:15,043 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:15 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:15,529 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:15 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:19,187 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:19 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:23,356 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:23 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:25,190 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:25 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:25,806 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:25 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:30,668 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:30 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:31,187 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:31 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:32,524 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:32 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:37,203 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:37 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:38,572 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:38 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:42,991 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:42 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:43,210 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:43 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:45,677 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:45 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:49,202 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:49 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:50,475 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:50 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:55,209 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:55 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:55,522 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:55 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:59,052 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:59 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:01,231 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:01 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:03,204 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:03,210 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:03,211 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:05,581 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:05 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:07,213 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:07 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:08,944 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:08 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:13,308 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:13 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:15,019 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:15 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:16,358 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:16 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:19,297 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:19 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:23,518 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:23 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:25,845 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:25 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:26,709 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:26 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:31,542 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:31 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:32,719 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:32 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:33,765 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:33 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:38,760 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:38 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:40,452 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:40 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:43,095 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:43 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:45,519 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:45 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:50,160 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:50 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:50,486 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:50 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:56,049 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:56 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:57,780 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:57 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:01,172 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:01 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:03,180 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:03,196 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:03,197 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:04,240 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:04 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:06,194 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:06 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:06,224 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:06 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:10,184 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:10 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:12,181 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:12 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:13,255 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:13 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:16,185 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:16 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:18,195 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:18 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:20,455 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:20 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:22,183 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:22 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:24,188 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:24 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:27,396 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:27 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:28,190 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:28 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:30,200 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:30 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:34,204 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:34 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:36,202 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:36 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:36,951 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:36 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:40,199 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:40 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:42,207 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:42 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:44,768 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:44 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:46,203 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:46 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:48,198 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:48 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:52,144 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:52 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:52,207 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:52 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:54,218 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:54 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:58,197 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:58 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:58,898 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:58 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:00,208 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:00 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:03,188 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:03,191 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:03,194 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:04,231 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:04 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:06,264 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:06 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:08,578 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:08 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:10,231 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:10 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:12,237 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:12 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:16,230 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:16 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:18,349 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:18 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:18,676 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:18 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:22,355 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:22 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:24,381 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:24 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:25,281 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:25 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:28,383 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:28 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:30,788 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:30 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:33,717 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:33 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:35,011 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:35 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:38,470 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:38 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:40,934 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:40 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:43,022 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:43 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:47,719 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:47 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:47,937 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:47 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:51,945 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:51 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:54,824 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:54 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:56,171 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:56 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:00,036 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:00 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:03,208 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:03,211 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:03,212 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:04,066 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:04 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:05,730 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:05 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:07,889 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:07 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:09,187 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:09 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:12,424 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:12 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:14,982 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:14 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:15,184 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:15 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:20,892 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:20 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:21,192 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:21 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:24,371 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:24 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:27,195 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:27 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:28,070 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:28 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:32,745 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:32 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:33,211 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:33 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:34,642 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:34 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:39,198 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:39 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:41,369 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:41 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:41,615 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:41 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:45,199 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:45 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:50,495 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:50 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:51,208 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:51 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:52,189 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:52 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:57,217 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:57 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:57,575 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:57 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:01,347 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:01 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:03,187 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:03,192 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:03,197 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:03,198 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:03 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:05,623 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:05 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:08,732 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:08 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:09,229 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:09 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:14,639 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:14 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:15,243 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:15 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:15,398 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:15 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:21,271 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:21 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:23,362 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:23 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:25,290 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:25 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:27,637 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:27 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:33,548 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:33 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:33,563 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:33 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:33,952 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:33 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:39,724 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:39 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:41,322 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:41 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:43,892 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:43 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:46,349 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:46 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:51,671 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:51 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:52,121 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:52 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:53,441 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:53 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:58,833 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:58 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:01,331 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:01 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:03,197 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:03,201 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:03,203 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:03,902 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:03 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:06,988 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:06 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:07,190 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:07 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:09,184 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:09 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:12,938 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:12 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:13,192 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:13 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:15,185 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:15 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:19,185 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:19 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:19,701 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:19 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:21,195 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:21 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:25,185 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:25 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:27,177 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:27 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:27,193 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:27 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:31,193 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:31 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:33,195 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:33 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:35,378 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:35 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:37,194 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:37 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:39,199 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:39 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:43,206 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:43 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:45,197 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:45 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:45,677 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:45 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:49,208 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:49 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:51,205 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:51 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:55,205 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:55 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:58,368 - database.models - INFO - [OK] Database migrations completed successfully
2025-05-31 13:25:58,368 - database.models - INFO - [OK] Database initialized successfully
2025-05-31 13:25:58,374 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,380 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,385 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,391 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,396 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,402 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,409 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,414 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,421 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,426 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,432 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,438 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,445 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,450 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,454 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,458 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,463 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,468 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,549 - market_data.advanced_market_data_manager - INFO - [DATA] Advanced Market Data Manager initialized
2025-05-31 13:25:58,549 - market_data.market_data_api - INFO - 📡 Market Data API initialized
2025-05-31 13:25:58,549 - market_data.websocket_streamer - INFO - 🌐 WebSocket Streamer initialized
2025-05-31 13:25:58,549 - notifications.notification_manager - INFO - 🔔 Notification Manager initialized
2025-05-31 13:25:58,549 - trading.live_trading_interface - INFO - [TRADING] Live Trading Interface initialized
2025-05-31 13:25:58,549 - strategies.live_strategy_engine - INFO - ⚡ Live Strategy Engine initialized
2025-05-31 13:25:58,549 - app - INFO - [OK] Advanced trading components with real-time market data initialized
2025-05-31 13:25:58,550 - app - INFO - [STARTUP] Money Circle initialized with config: default
2025-05-31 13:25:58,552 - market_data.market_data_api - INFO - ✅ Market Data API routes configured
2025-05-31 13:25:58,552 - app - INFO - [OK] Market Data API routes configured
2025-05-31 13:25:58,552 - app - INFO - [OK] Market Data WebSocket routes configured
2025-05-31 13:25:58,552 - app - INFO - [OK] Routes configured
2025-05-31 13:25:58,553 - app - INFO - [OK] Web application configured
2025-05-31 13:25:58,568 - database.models - INFO - [OK] Database migrations completed successfully
2025-05-31 13:25:58,568 - database.models - INFO - [OK] Database initialized successfully
2025-05-31 13:25:58,576 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,581 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,585 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,591 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,596 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,600 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,605 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,610 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,615 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,622 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,628 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,633 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,639 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,644 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,649 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,653 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,658 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,663 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,663 - market_data.advanced_market_data_manager - INFO - [DATA] Advanced Market Data Manager initialized
2025-05-31 13:25:58,663 - market_data.market_data_api - INFO - 📡 Market Data API initialized
2025-05-31 13:25:58,663 - market_data.websocket_streamer - INFO - 🌐 WebSocket Streamer initialized
2025-05-31 13:25:58,663 - notifications.notification_manager - INFO - 🔔 Notification Manager initialized
2025-05-31 13:25:58,663 - trading.live_trading_interface - INFO - [TRADING] Live Trading Interface initialized
2025-05-31 13:25:58,663 - strategies.live_strategy_engine - INFO - ⚡ Live Strategy Engine initialized
2025-05-31 13:25:58,663 - app - INFO - [OK] Advanced trading components with real-time market data initialized
2025-05-31 13:25:58,663 - app - INFO - [STARTUP] Money Circle initialized with config: default
2025-05-31 13:25:58,665 - market_data.market_data_api - INFO - ✅ Market Data API routes configured
2025-05-31 13:25:58,665 - app - INFO - [OK] Market Data API routes configured
2025-05-31 13:25:58,666 - app - INFO - [OK] Market Data WebSocket routes configured
2025-05-31 13:25:58,666 - app - INFO - [OK] Routes configured
2025-05-31 13:25:58,667 - app - INFO - [OK] Web application configured
2025-05-31 13:46:33,471 - database.models - INFO - [OK] Database migrations completed successfully
2025-05-31 13:46:33,472 - database.models - INFO - [OK] Database initialized successfully
2025-05-31 13:46:33,477 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,482 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,487 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,492 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,496 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,501 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,505 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,510 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,516 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,521 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,528 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,532 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,537 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,542 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,547 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,552 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,558 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,562 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,645 - market_data.advanced_market_data_manager - INFO - [DATA] Advanced Market Data Manager initialized
2025-05-31 13:46:33,646 - market_data.market_data_api - INFO - [API] Market Data API initialized
2025-05-31 13:46:33,646 - market_data.websocket_streamer - INFO - [WS] WebSocket Streamer initialized
2025-05-31 13:46:33,646 - notifications.notification_manager - INFO - [NOTIFY] Notification Manager initialized
2025-05-31 13:46:33,646 - trading.live_trading_interface - INFO - [TRADING] Live Trading Interface initialized
2025-05-31 13:46:33,646 - strategies.live_strategy_engine - INFO - [STRATEGY] Live Strategy Engine initialized
2025-05-31 13:46:33,647 - __main__ - INFO - [OK] Advanced trading components with real-time market data initialized
2025-05-31 13:46:33,647 - __main__ - INFO - [STARTUP] Money Circle initialized with config: development
2025-05-31 13:46:33,650 - market_data.market_data_api - INFO - [API] Market Data API routes configured
2025-05-31 13:46:33,650 - __main__ - INFO - [OK] Market Data API routes configured
2025-05-31 13:46:33,651 - __main__ - INFO - [OK] Market Data WebSocket routes configured
2025-05-31 13:46:33,651 - __main__ - INFO - [OK] Routes configured
2025-05-31 13:46:33,652 - __main__ - INFO - [OK] Web application configured
2025-05-31 13:46:33,659 - __main__ - INFO - [SERVER] Money Circle running at http://localhost:8080
2025-05-31 13:46:33,659 - __main__ - INFO - [READY] Platform ready for Money Circle investment club members
2025-05-31 13:46:33,659 - __main__ - INFO - [DATA] Advanced market data manager started
2025-05-31 13:46:33,659 - __main__ - INFO - 🌐 Market data WebSocket streamer subscribed to updates
2025-05-31 13:46:33,660 - __main__ - INFO - 📡 Notification system started
2025-05-31 13:46:33,660 - __main__ - INFO - ⚡ Live trading monitoring started
2025-05-31 13:46:33,660 - __main__ - INFO - 🤖 Strategy automation started
2025-05-31 13:46:33,660 - market_data.advanced_market_data_manager - INFO - [DATA] Starting advanced market data manager...
2025-05-31 13:46:33,661 - notifications.notification_manager - INFO - 📡 Starting notification delivery system...
2025-05-31 13:46:33,661 - trading.live_trading_interface - INFO - 📊 Starting real-time trading monitoring...
2025-05-31 13:46:33,661 - strategies.live_strategy_engine - INFO - 🚀 Starting live strategy automation...
2025-05-31 13:46:33,661 - market_data.advanced_market_data_manager - INFO - [DATA] Connecting to Binance WebSocket...
2025-05-31 13:46:33,682 - market_data.advanced_market_data_manager - INFO - [DATA] Connecting to HTX WebSocket...
2025-05-31 13:46:33,683 - market_data.advanced_market_data_manager - INFO - 🔄 Connecting to Bybit Spot WebSocket...
2025-05-31 13:46:33,691 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:46:33,692 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 0.0% (0 excellent, 0 good)
2025-05-31 13:46:33,692 - notifications.notification_manager - ERROR - ❌ Error cleaning up notifications: no such column: expires_at
2025-05-31 13:46:35,159 - market_data.advanced_market_data_manager - INFO - [DATA] Connected to Binance WebSocket
2025-05-31 13:46:35,241 - market_data.advanced_market_data_manager - INFO - ✅ Connected to Bybit Spot WebSocket
2025-05-31 13:46:35,242 - market_data.advanced_market_data_manager - INFO - 📡 Subscribed to Bybit tickers: ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
2025-05-31 13:46:35,313 - market_data.advanced_market_data_manager - INFO - [DATA] Connected to HTX WebSocket
2025-05-31 13:46:35,539 - market_data.advanced_market_data_manager - INFO - ✅ Bybit subscription confirmed: subscribe
2025-05-31 13:46:37,475 - aiohttp.access - INFO - ::1 [31/May/2025:12:46:37 -0600] "GET / HTTP/1.1" 302 132 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:46:37,489 - aiohttp.access - INFO - ::1 [31/May/2025:12:46:37 -0600] "GET /login HTTP/1.1" 200 17109 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:46:37,526 - aiohttp.access - INFO - ::1 [31/May/2025:12:46:37 -0600] "GET /static/js/common.js HTTP/1.1" 304 179 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:46:37,624 - aiohttp.access - INFO - ::1 [31/May/2025:12:46:37 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 172 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:46:41,356 - auth.user_manager - INFO - [AUTH] Successful login: trader_alex from ::1
2025-05-31 13:46:41,357 - auth.user_manager - INFO - [AUTH] Session created for user: trader_alex
2025-05-31 13:46:41,357 - __main__ - INFO - [OK] User logged in: trader_alex
2025-05-31 13:46:41,358 - aiohttp.access - INFO - ::1 [31/May/2025:12:46:41 -0600] "POST /login HTTP/1.1" 302 408 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:46:41,363 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 13:46:41,402 - aiohttp.server - ERROR - Error handling request
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_protocol.py", line 477, in _handle_request
    resp = await request_handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_app.py", line 567, in _handle
    return await handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_middlewares.py", line 117, in impl
    return await handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_session\__init__.py", line 191, in factory
    response = await handler(request)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\app.py", line 206, in auth_middleware
    return await handler(request)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\dashboards\personal_dashboard.py", line 60, in serve_personal_dashboard
    return aiohttp_jinja2.render_template('personal_dashboard.html', request, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_jinja2\__init__.py", line 176, in render_template
    response.text = render_string(template_name, request, context, app_key=app_key)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_jinja2\__init__.py", line 139, in render_string
    return template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\personal_dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\base.html", line 40, in top-level template code
    {% include 'components/header.html' %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\components\header.html", line 15, in top-level template code
    <a href="/dashboard" class="nav-link {% if request.path == '/dashboard' or request.path == '/personal' %}active{% endif %}">
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
jinja2.exceptions.UndefinedError: 'request' is undefined
2025-05-31 13:46:41,406 - aiohttp.access - INFO - ::1 [31/May/2025:12:46:41 -0600] "GET /dashboard HTTP/1.1" 500 336 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:47:33,687 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:47:33,687 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:48:33,697 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:48:33,697 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:49:33,702 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:49:33,702 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:50:33,717 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:50:33,717 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:51:33,731 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:51:33,731 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:52:18,078 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 13:52:18,108 - aiohttp.server - ERROR - Error handling request
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_protocol.py", line 477, in _handle_request
    resp = await request_handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_app.py", line 567, in _handle
    return await handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_middlewares.py", line 117, in impl
    return await handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_session\__init__.py", line 191, in factory
    response = await handler(request)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\app.py", line 206, in auth_middleware
    return await handler(request)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\dashboards\personal_dashboard.py", line 60, in serve_personal_dashboard
    # Render using template
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_jinja2\__init__.py", line 176, in render_template
    response.text = render_string(template_name, request, context, app_key=app_key)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_jinja2\__init__.py", line 139, in render_string
    return template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\personal_dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\base.html", line 40, in top-level template code
    {% include 'components/header.html' %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\components\header.html", line 15, in top-level template code
    <a href="/dashboard" class="nav-link {% if request.path == '/dashboard' or request.path == '/personal' %}active{% endif %}">
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
jinja2.exceptions.UndefinedError: 'request' is undefined
2025-05-31 13:52:18,110 - aiohttp.access - INFO - ::1 [31/May/2025:12:52:18 -0600] "GET /dashboard HTTP/1.1" 500 336 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:52:18,634 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 13:52:18,657 - aiohttp.server - ERROR - Error handling request
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_protocol.py", line 477, in _handle_request
    resp = await request_handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_app.py", line 567, in _handle
    return await handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_middlewares.py", line 117, in impl
    return await handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_session\__init__.py", line 191, in factory
    response = await handler(request)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\app.py", line 206, in auth_middleware
    return await handler(request)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\dashboards\personal_dashboard.py", line 60, in serve_personal_dashboard
    # Render using template
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_jinja2\__init__.py", line 176, in render_template
    response.text = render_string(template_name, request, context, app_key=app_key)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_jinja2\__init__.py", line 139, in render_string
    return template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\personal_dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\base.html", line 40, in top-level template code
    {% include 'components/header.html' %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\components\header.html", line 15, in top-level template code
    <a href="/dashboard" class="nav-link {% if request.path == '/dashboard' or request.path == '/personal' %}active{% endif %}">
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
jinja2.exceptions.UndefinedError: 'request' is undefined
2025-05-31 13:52:18,658 - aiohttp.access - INFO - ::1 [31/May/2025:12:52:18 -0600] "GET /dashboard HTTP/1.1" 500 336 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:52:33,734 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:52:33,734 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
