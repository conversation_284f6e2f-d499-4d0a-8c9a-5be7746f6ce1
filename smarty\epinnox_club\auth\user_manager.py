#!/usr/bin/env python3
"""
Money Circle User Management
Enhanced user authentication and management system.
"""

import hashlib
import secrets
import logging
import time
import re
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Any, Tuple
import bcrypt
from database.models import DatabaseManager, User, MembershipAgreement

logger = logging.getLogger(__name__)

class UserManager:
    """Enhanced user management with secure authentication."""

    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.failed_attempts: Dict[str, List[float]] = {}
        self.active_sessions: Dict[str, Dict[str, Any]] = {}

        # Security settings
        self.max_attempts = 5
        self.lockout_duration = 300  # 5 minutes
        self.session_timeout = 24 * 60 * 60  # 24 hours
        self.registration_rate_limit = 5  # registrations per hour per IP

        # Initialize with default admin user if none exists
        self._create_default_admin()

    def _create_default_admin(self):
        """Create default admin user if no users exist."""
        try:
            cursor = self.db.conn.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]

            if user_count == 0:
                # Create default admin user (epinnox)
                admin_user = User(
                    username="epinnox",
                    email="<EMAIL>",
                    hashed_password=self._hash_password("securepass123"),
                    role="admin",
                    email_verified=True,
                    agreement_accepted=True,
                    is_active=True
                )

                if self.create_user(admin_user):
                    logger.info("✅ Default admin user created: epinnox")
                else:
                    logger.error("❌ Failed to create default admin user")

        except Exception as e:
            logger.error(f"Error creating default admin: {e}")

    def _hash_password(self, password: str) -> str:
        """Hash password using bcrypt."""
        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        return hashed.decode('utf-8')

    def _verify_password(self, password: str, hashed: str) -> bool:
        """Verify password against hash."""
        try:
            return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
        except Exception as e:
            logger.error(f"Password verification error: {e}")
            return False

    def _is_locked_out(self, identifier: str) -> bool:
        """Check if user/IP is locked out due to failed attempts."""
        if identifier not in self.failed_attempts:
            return False

        current_time = time.time()
        attempts = self.failed_attempts[identifier]

        # Remove old attempts outside lockout window
        cutoff_time = current_time - self.lockout_duration
        self.failed_attempts[identifier] = [t for t in attempts if t > cutoff_time]

        # Check if still locked out
        return len(self.failed_attempts[identifier]) >= self.max_attempts

    def _record_failed_attempt(self, identifier: str):
        """Record a failed login attempt."""
        current_time = time.time()
        if identifier not in self.failed_attempts:
            self.failed_attempts[identifier] = []

        self.failed_attempts[identifier].append(current_time)
        logger.warning(f"🚫 Failed login attempt for: {identifier}")

    def _clear_failed_attempts(self, identifier: str):
        """Clear failed attempts for successful login."""
        if identifier in self.failed_attempts:
            del self.failed_attempts[identifier]

    def validate_registration_data(self, username: str, email: str, password: str) -> Tuple[bool, str]:
        """Validate registration data and return (is_valid, error_message)."""
        # Username validation
        if not username or len(username) < 3:
            return False, "Username must be at least 3 characters long"

        if len(username) > 30:
            return False, "Username must be less than 30 characters"

        if not re.match(r'^[a-zA-Z0-9_-]+$', username):
            return False, "Username can only contain letters, numbers, hyphens, and underscores"

        # Email validation
        if not email:
            return False, "Email is required"

        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            return False, "Please enter a valid email address"

        # Password validation
        if not password or len(password) < 8:
            return False, "Password must be at least 8 characters long"

        if len(password) > 128:
            return False, "Password must be less than 128 characters"

        # Check for mixed case, numbers
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)

        if not (has_upper and has_lower and has_digit):
            return False, "Password must contain uppercase, lowercase, and numeric characters"

        # Check for existing username
        if self.get_user_by_username(username):
            return False, "Username already exists"

        # Check for existing email
        if self.get_user_by_email(email):
            return False, "Email already registered"

        return True, ""

    def check_registration_rate_limit(self, ip_address: str) -> bool:
        """Check if IP address has exceeded registration rate limit."""
        if not ip_address:
            return True  # Allow if no IP provided

        current_time = time.time()
        cutoff_time = current_time - 3600  # 1 hour ago

        # Count recent registrations from this IP
        try:
            cursor = self.db.conn.execute("""
                SELECT COUNT(*) FROM users
                WHERE date_joined > datetime('now', '-1 hour')
                AND id IN (
                    SELECT user_id FROM membership_agreements
                    WHERE ip_address = ?
                )
            """, (ip_address,))

            count = cursor.fetchone()[0]
            return count < self.registration_rate_limit

        except Exception as e:
            logger.error(f"Error checking registration rate limit: {e}")
            return True  # Allow on error

    def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email."""
        try:
            cursor = self.db.conn.execute("""
                SELECT id, username, email, hashed_password, role, date_joined,
                       last_login, email_verified, agreement_accepted, is_active
                FROM users WHERE email = ? AND is_active = 1
            """, (email,))

            row = cursor.fetchone()
            if row:
                return User(
                    id=row[0],
                    username=row[1],
                    email=row[2],
                    hashed_password=row[3],
                    role=row[4],
                    date_joined=datetime.fromisoformat(row[5]) if row[5] else None,
                    last_login=datetime.fromisoformat(row[6]) if row[6] else None,
                    email_verified=bool(row[7]),
                    agreement_accepted=bool(row[8]),
                    is_active=bool(row[9])
                )
            return None

        except Exception as e:
            logger.error(f"Error getting user by email: {e}")
            return None

    def register_user(self, username: str, email: str, password: str, ip_address: str = "") -> Tuple[bool, str, Optional[User]]:
        """Register a new user with validation."""
        # Check rate limiting
        if not self.check_registration_rate_limit(ip_address):
            return False, "Too many registration attempts. Please try again later.", None

        # Validate registration data
        is_valid, error_msg = self.validate_registration_data(username, email, password)
        if not is_valid:
            return False, error_msg, None

        # Create user
        hashed_password = self._hash_password(password)
        user = User(
            username=username,
            email=email,
            hashed_password=hashed_password,
            role="member",
            email_verified=False,
            agreement_accepted=False,
            is_active=True
        )

        if self.create_user(user):
            logger.info(f"✅ New user registered: {username} ({email})")
            return True, "Registration successful", user
        else:
            return False, "Registration failed. Please try again.", None

    def create_user(self, user: User) -> bool:
        """Create a new user."""
        try:
            cursor = self.db.conn.execute("""
                INSERT INTO users (username, email, hashed_password, role,
                                 email_verified, agreement_accepted, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (user.username, user.email, user.hashed_password, user.role,
                  user.email_verified, user.agreement_accepted, user.is_active))

            self.db.conn.commit()
            user.id = cursor.lastrowid
            logger.info(f"✅ User created: {user.username} ({user.role})")
            return True

        except Exception as e:
            logger.error(f"Error creating user: {e}")
            return False

    def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username."""
        try:
            cursor = self.db.conn.execute("""
                SELECT id, username, email, hashed_password, role, date_joined,
                       last_login, email_verified, agreement_accepted, is_active
                FROM users WHERE username = ? AND is_active = 1
            """, (username,))

            row = cursor.fetchone()
            if row:
                return User(
                    id=row[0],
                    username=row[1],
                    email=row[2],
                    hashed_password=row[3],
                    role=row[4],
                    date_joined=datetime.fromisoformat(row[5]) if row[5] else None,
                    last_login=datetime.fromisoformat(row[6]) if row[6] else None,
                    email_verified=bool(row[7]) if row[7] is not None else False,
                    agreement_accepted=bool(row[8]) if row[8] is not None else False,
                    is_active=bool(row[9])
                )
            return None

        except Exception as e:
            logger.error(f"Error getting user: {e}")
            return None

    def get_user_by_id(self, user_id: int) -> Optional[User]:
        """Get user by ID."""
        try:
            cursor = self.db.conn.execute("""
                SELECT id, username, email, hashed_password, role, date_joined, is_active
                FROM users WHERE id = ? AND is_active = 1
            """, (user_id,))

            row = cursor.fetchone()
            if row:
                return User(
                    id=row[0],
                    username=row[1],
                    email=row[2],
                    hashed_password=row[3],
                    role=row[4],
                    date_joined=datetime.fromisoformat(row[5]) if row[5] else None,
                    is_active=bool(row[6])
                )
            return None

        except Exception as e:
            logger.error(f"Error getting user by ID: {e}")
            return None

    def authenticate_user(self, username: str, password: str, ip_address: str = "") -> Optional[User]:
        """Authenticate user with rate limiting."""
        # Check for lockout
        if self._is_locked_out(username) or self._is_locked_out(ip_address):
            logger.warning(f"🔒 Login blocked due to lockout: {username} from {ip_address}")
            return None

        # Get user
        user = self.get_user_by_username(username)
        if not user:
            self._record_failed_attempt(username)
            self._record_failed_attempt(ip_address)
            return None

        # Verify password
        if self._verify_password(password, user.hashed_password):
            self._clear_failed_attempts(username)
            self._clear_failed_attempts(ip_address)
            logger.info(f"✅ Successful login: {username} from {ip_address}")
            return user
        else:
            self._record_failed_attempt(username)
            self._record_failed_attempt(ip_address)
            return None

    def create_session(self, user: User) -> str:
        """Create a new session for user."""
        session_id = secrets.token_urlsafe(32)
        session_data = {
            'user_id': user.id,
            'username': user.username,
            'role': user.role,
            'created_at': time.time(),
            'last_activity': time.time()
        }

        self.active_sessions[session_id] = session_data
        logger.info(f"🎫 Session created for user: {user.username}")
        return session_id

    def validate_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Validate and refresh session."""
        if session_id not in self.active_sessions:
            return None

        session = self.active_sessions[session_id]
        current_time = time.time()

        # Check if session expired
        if current_time - session['last_activity'] > self.session_timeout:
            del self.active_sessions[session_id]
            logger.info(f"⏰ Session expired for user: {session['username']}")
            return None

        # Update last activity
        session['last_activity'] = current_time
        return session

    def destroy_session(self, session_id: str) -> bool:
        """Destroy a session."""
        if session_id in self.active_sessions:
            username = self.active_sessions[session_id]['username']
            del self.active_sessions[session_id]
            logger.info(f"🚪 Session destroyed for user: {username}")
            return True
        return False

    def get_all_users(self) -> List[User]:
        """Get all active users (admin only)."""
        try:
            cursor = self.db.conn.execute("""
                SELECT id, username, email, hashed_password, role, date_joined, is_active
                FROM users WHERE is_active = 1 ORDER BY date_joined DESC
            """)

            users = []
            for row in cursor.fetchall():
                users.append(User(
                    id=row[0],
                    username=row[1],
                    email=row[2],
                    hashed_password=row[3],
                    role=row[4],
                    date_joined=datetime.fromisoformat(row[5]) if row[5] else None,
                    is_active=bool(row[6])
                ))

            return users

        except Exception as e:
            logger.error(f"Error getting all users: {e}")
            return []

    def update_user_role(self, user_id: int, new_role: str) -> bool:
        """Update user role (admin only)."""
        try:
            if new_role not in ['admin', 'member', 'viewer']:
                return False

            self.db.conn.execute("""
                UPDATE users SET role = ? WHERE id = ?
            """, (new_role, user_id))

            self.db.conn.commit()
            logger.info(f"✅ User role updated: ID {user_id} -> {new_role}")
            return True

        except Exception as e:
            logger.error(f"Error updating user role: {e}")
            return False

    def deactivate_user(self, user_id: int) -> bool:
        """Deactivate user account (admin only)."""
        try:
            self.db.conn.execute("""
                UPDATE users SET is_active = 0 WHERE id = ?
            """, (user_id,))

            self.db.conn.commit()
            logger.info(f"🚫 User deactivated: ID {user_id}")
            return True

        except Exception as e:
            logger.error(f"Error deactivating user: {e}")
            return False

    def record_agreement_acceptance(self, user_id: int, agreement_text: str,
                                  ip_address: str = "", user_agent: str = "") -> bool:
        """Record user's acceptance of membership agreement."""
        try:
            # Insert agreement record
            cursor = self.db.conn.execute("""
                INSERT INTO membership_agreements
                (user_id, agreement_version, agreement_text, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?)
            """, (user_id, "v1.0", agreement_text, ip_address, user_agent))

            # Update user's agreement status
            self.db.conn.execute("""
                UPDATE users SET agreement_accepted = TRUE WHERE id = ?
            """, (user_id,))

            self.db.conn.commit()
            logger.info(f"✅ Agreement accepted by user ID: {user_id}")
            return True

        except Exception as e:
            logger.error(f"Error recording agreement acceptance: {e}")
            return False

    def update_last_login(self, user_id: int) -> bool:
        """Update user's last login timestamp."""
        try:
            self.db.conn.execute("""
                UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?
            """, (user_id,))

            self.db.conn.commit()
            return True

        except Exception as e:
            logger.error(f"Error updating last login: {e}")
            return False

    def get_agreement_status(self, user_id: int) -> bool:
        """Check if user has accepted the membership agreement."""
        try:
            cursor = self.db.conn.execute("""
                SELECT agreement_accepted FROM users WHERE id = ?
            """, (user_id,))

            row = cursor.fetchone()
            return bool(row[0]) if row else False

        except Exception as e:
            logger.error(f"Error checking agreement status: {e}")
            return False
