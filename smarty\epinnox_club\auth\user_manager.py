#!/usr/bin/env python3
"""
Money Circle User Management
Enhanced user authentication and management system.
"""

import hashlib
import secrets
import logging
import time
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Any
import bcrypt
from database.models import DatabaseManager, User

logger = logging.getLogger(__name__)

class UserManager:
    """Enhanced user management with secure authentication."""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.failed_attempts: Dict[str, List[float]] = {}
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        
        # Security settings
        self.max_attempts = 5
        self.lockout_duration = 300  # 5 minutes
        self.session_timeout = 24 * 60 * 60  # 24 hours
        
        # Initialize with default admin user if none exists
        self._create_default_admin()
    
    def _create_default_admin(self):
        """Create default admin user if no users exist."""
        try:
            cursor = self.db.conn.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            
            if user_count == 0:
                # Create default admin user (epinnox)
                admin_user = User(
                    username="epinnox",
                    email="<EMAIL>",
                    hashed_password=self._hash_password("securepass123"),
                    role="admin",
                    is_active=True
                )
                
                if self.create_user(admin_user):
                    logger.info("✅ Default admin user created: epinnox")
                else:
                    logger.error("❌ Failed to create default admin user")
                    
        except Exception as e:
            logger.error(f"Error creating default admin: {e}")
    
    def _hash_password(self, password: str) -> str:
        """Hash password using bcrypt."""
        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        return hashed.decode('utf-8')
    
    def _verify_password(self, password: str, hashed: str) -> bool:
        """Verify password against hash."""
        try:
            return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
        except Exception as e:
            logger.error(f"Password verification error: {e}")
            return False
    
    def _is_locked_out(self, identifier: str) -> bool:
        """Check if user/IP is locked out due to failed attempts."""
        if identifier not in self.failed_attempts:
            return False
        
        current_time = time.time()
        attempts = self.failed_attempts[identifier]
        
        # Remove old attempts outside lockout window
        cutoff_time = current_time - self.lockout_duration
        self.failed_attempts[identifier] = [t for t in attempts if t > cutoff_time]
        
        # Check if still locked out
        return len(self.failed_attempts[identifier]) >= self.max_attempts
    
    def _record_failed_attempt(self, identifier: str):
        """Record a failed login attempt."""
        current_time = time.time()
        if identifier not in self.failed_attempts:
            self.failed_attempts[identifier] = []
        
        self.failed_attempts[identifier].append(current_time)
        logger.warning(f"🚫 Failed login attempt for: {identifier}")
    
    def _clear_failed_attempts(self, identifier: str):
        """Clear failed attempts for successful login."""
        if identifier in self.failed_attempts:
            del self.failed_attempts[identifier]
    
    def create_user(self, user: User) -> bool:
        """Create a new user."""
        try:
            cursor = self.db.conn.execute("""
                INSERT INTO users (username, email, hashed_password, role, is_active)
                VALUES (?, ?, ?, ?, ?)
            """, (user.username, user.email, user.hashed_password, user.role, user.is_active))
            
            self.db.conn.commit()
            user.id = cursor.lastrowid
            logger.info(f"✅ User created: {user.username} ({user.role})")
            return True
            
        except Exception as e:
            logger.error(f"Error creating user: {e}")
            return False
    
    def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username."""
        try:
            cursor = self.db.conn.execute("""
                SELECT id, username, email, hashed_password, role, date_joined, is_active
                FROM users WHERE username = ? AND is_active = 1
            """, (username,))
            
            row = cursor.fetchone()
            if row:
                return User(
                    id=row[0],
                    username=row[1],
                    email=row[2],
                    hashed_password=row[3],
                    role=row[4],
                    date_joined=datetime.fromisoformat(row[5]) if row[5] else None,
                    is_active=bool(row[6])
                )
            return None
            
        except Exception as e:
            logger.error(f"Error getting user: {e}")
            return None
    
    def get_user_by_id(self, user_id: int) -> Optional[User]:
        """Get user by ID."""
        try:
            cursor = self.db.conn.execute("""
                SELECT id, username, email, hashed_password, role, date_joined, is_active
                FROM users WHERE id = ? AND is_active = 1
            """, (user_id,))
            
            row = cursor.fetchone()
            if row:
                return User(
                    id=row[0],
                    username=row[1],
                    email=row[2],
                    hashed_password=row[3],
                    role=row[4],
                    date_joined=datetime.fromisoformat(row[5]) if row[5] else None,
                    is_active=bool(row[6])
                )
            return None
            
        except Exception as e:
            logger.error(f"Error getting user by ID: {e}")
            return None
    
    def authenticate_user(self, username: str, password: str, ip_address: str = "") -> Optional[User]:
        """Authenticate user with rate limiting."""
        # Check for lockout
        if self._is_locked_out(username) or self._is_locked_out(ip_address):
            logger.warning(f"🔒 Login blocked due to lockout: {username} from {ip_address}")
            return None
        
        # Get user
        user = self.get_user_by_username(username)
        if not user:
            self._record_failed_attempt(username)
            self._record_failed_attempt(ip_address)
            return None
        
        # Verify password
        if self._verify_password(password, user.hashed_password):
            self._clear_failed_attempts(username)
            self._clear_failed_attempts(ip_address)
            logger.info(f"✅ Successful login: {username} from {ip_address}")
            return user
        else:
            self._record_failed_attempt(username)
            self._record_failed_attempt(ip_address)
            return None
    
    def create_session(self, user: User) -> str:
        """Create a new session for user."""
        session_id = secrets.token_urlsafe(32)
        session_data = {
            'user_id': user.id,
            'username': user.username,
            'role': user.role,
            'created_at': time.time(),
            'last_activity': time.time()
        }
        
        self.active_sessions[session_id] = session_data
        logger.info(f"🎫 Session created for user: {user.username}")
        return session_id
    
    def validate_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Validate and refresh session."""
        if session_id not in self.active_sessions:
            return None
        
        session = self.active_sessions[session_id]
        current_time = time.time()
        
        # Check if session expired
        if current_time - session['last_activity'] > self.session_timeout:
            del self.active_sessions[session_id]
            logger.info(f"⏰ Session expired for user: {session['username']}")
            return None
        
        # Update last activity
        session['last_activity'] = current_time
        return session
    
    def destroy_session(self, session_id: str) -> bool:
        """Destroy a session."""
        if session_id in self.active_sessions:
            username = self.active_sessions[session_id]['username']
            del self.active_sessions[session_id]
            logger.info(f"🚪 Session destroyed for user: {username}")
            return True
        return False
    
    def get_all_users(self) -> List[User]:
        """Get all active users (admin only)."""
        try:
            cursor = self.db.conn.execute("""
                SELECT id, username, email, hashed_password, role, date_joined, is_active
                FROM users WHERE is_active = 1 ORDER BY date_joined DESC
            """)
            
            users = []
            for row in cursor.fetchall():
                users.append(User(
                    id=row[0],
                    username=row[1],
                    email=row[2],
                    hashed_password=row[3],
                    role=row[4],
                    date_joined=datetime.fromisoformat(row[5]) if row[5] else None,
                    is_active=bool(row[6])
                ))
            
            return users
            
        except Exception as e:
            logger.error(f"Error getting all users: {e}")
            return []
    
    def update_user_role(self, user_id: int, new_role: str) -> bool:
        """Update user role (admin only)."""
        try:
            if new_role not in ['admin', 'member', 'viewer']:
                return False
            
            self.db.conn.execute("""
                UPDATE users SET role = ? WHERE id = ?
            """, (new_role, user_id))
            
            self.db.conn.commit()
            logger.info(f"✅ User role updated: ID {user_id} -> {new_role}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating user role: {e}")
            return False
    
    def deactivate_user(self, user_id: int) -> bool:
        """Deactivate user account (admin only)."""
        try:
            self.db.conn.execute("""
                UPDATE users SET is_active = 0 WHERE id = ?
            """, (user_id,))
            
            self.db.conn.commit()
            logger.info(f"🚫 User deactivated: ID {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deactivating user: {e}")
            return False
